import { useMemo, type FC } from 'react';
import SectionLayout from '@shared/components/Organism/Objects/Common/Section.layout';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import Button from '@shared/uikit/Button';
import { RichTextView } from '@shared/uikit/RichText';
import useTranslation from '@shared/utils/hooks/useTranslation';
import InfoCardList from '../components/InfoCardsList/InfoCardList';
import classes from './CandidateAbout.module.scss';
import type { InfoCardListItem } from '../components/InfoCardsList/InfoCardList';
import type { BaseCandidateSectionProp } from '@shared/types/candidates';

const CandidateContactsSection: FC<BaseCandidateSectionProp> = ({
  candidate,
}) => {
  const appDispatch = useGlobalDispatch();

  const { t } = useTranslation();

  const onClickSendEmail = () => {
    appDispatch({
      type: 'TOGGLE_CANDIDATE_MANAGER',
      payload: {
        isOpen: true,
        tab: 'emails',
        id: candidate.id,
        enableNavigate: true,
      },
    });
  };
  const fullAddress = useMemo(
    () =>
      candidate.fullAddress ? (
        <RichTextView
          className={classes.richTextView}
          html={candidate.fullAddress ?? ''}
          typographyProps={{
            size: 15,
            color: 'thirdText',
          }}
          showMore
        />
      ) : null,
    [candidate.fullAddress]
  );
  const items = useMemo<InfoCardListItem[]>(
    () => [
      {
        key: 'envelope',
        icon: 'envelope',
        title: t('email'),
        value: candidate.profile.email?.value,
        renderRight: (
          <Button
            onClick={onClickSendEmail}
            schema="semi-transparent3"
            label={t('send_email')}
          />
        ),
      },
      {
        key: 'full-address',
        icon: 'full-address',
        title: t('full_address'),
        value: fullAddress,
      },
    ],
    [candidate, fullAddress, t]
  );

  const visibleItems = useMemo(
    () => items.filter((item) => !!item.value),
    [items]
  );

  if (!visibleItems.length) return null;

  return (
    <SectionLayout visibleActionButton title={t('contact')}>
      <InfoCardList items={visibleItems} />
    </SectionLayout>
  );
};

export default CandidateContactsSection;
