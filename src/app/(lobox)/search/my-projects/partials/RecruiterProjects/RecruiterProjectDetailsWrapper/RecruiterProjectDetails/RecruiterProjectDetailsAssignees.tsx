import { useCallback } from 'react';
import AssigneeNetworkButtons from '@shared/components/molecules/AssigneeNetworkButtons';
import SearchCard from '@shared/components/Organism/SearchCard';
import useAccessibility from '@shared/hooks/useAccessibility';
import useGetAppObject from '@shared/hooks/useGetAppObject';
import Flex from '@shared/uikit/Flex';
import Grid from '@shared/uikit/Grid';
import type { UserApiResponse } from '@shared/types/user';

interface RecruiterProjectDetailsAssigneesProps {
  assignees: UserApiResponse[];
}

const RecruiterProjectDetailsAssignees: React.FunctionComponent<
  RecruiterProjectDetailsAssigneesProps
> = ({ assignees }) => {
  const { authUser } = useGetAppObject();
  const { accessName } = useAccessibility();
  const filteredAssignees = assignees.reduce((acc, assignee) => {
    if (assignee.id !== authUser?.id) {
      acc.push(assignee);
    } else {
      acc.unshift(assignee);
    }

    return acc;
  }, [] as UserApiResponse[]);
  const NetworkButtons = useCallback(
    (collab: UserApiResponse) => (
      <AssigneeNetworkButtons hasCreateMeeting user={collab} />
    ),
    []
  );

  return (
    <Flex className="mt-32">
      {filteredAssignees.length && (
        <Grid container spacing={2}>
          {filteredAssignees.map((collab) => (
            <Grid size={6} key={`collaborator_${collab.id}`}>
              <SearchCard
                imgSrc={collab.croppedImageUrl as string}
                firstText={collab.fullName as string}
                secondText={accessName(collab as any)}
                thirdText={collab.occupation?.label as string}
                fourthText={collab.location.title as string}
                isHoverAble={false}
                bottomComponent={() => NetworkButtons(collab)}
                classNames={{
                  bottomWrapper: '!h-[unset]',
                }}
              />
            </Grid>
          ))}
        </Grid>
      )}
    </Flex>
  );
};

export default RecruiterProjectDetailsAssignees;
