import useMeetingTemplates from '@shared/hooks/schedules/useMeetingTemplates';
import React, { useState } from 'react';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import FixedRightSideModalDialog from '@shared/uikit/Modal/FixedRightSideModalDialog/FixedRightSideModalDialog.component';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import ModalFooter from '@shared/uikit/Modal/ModalFooter';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import {
  deleteDefaultMeetingTemplate,
  getMeetingTemplate,
  putMeetingTemplate,
  setDefaultMeetingTemplate,
} from '@shared/utils/api/template';
import { QueryKeys } from '@shared/utils/constants';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import useReactMutation from '@shared/utils/hooks/useReactMutation';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { EnhancedTemplateForm } from '../AutomationModal/components';
import TemplateList from '../AutomationModal/components/TemplateList';
import type { NormalizedTemplate } from '../AutomationModal/types/template.types';
import type { NormalizedMeetingTemplate } from '@shared/hooks/schedules/useMeetingTemplates';

const transformMeetingTemplateToNormalized = (
  meetingTemplate: NormalizedMeetingTemplate
): NormalizedTemplate => ({
  id: meetingTemplate?.id,
  title: meetingTemplate?.title,
  subject: meetingTemplate?.subject,
  message: meetingTemplate?.message,
  fileIds: meetingTemplate?.fileIds,
  default: meetingTemplate?.default,
});

const TemplateSelection = () => {
  const { t } = useTranslation();
  const { handleChangeParams } = useCustomParams();
  const [showForm, setShowForm] = useState(false);
  const [editId, setEditId] = useState('');
  const [editedValue, setEditedValue] = useState<
    NormalizedMeetingTemplate | undefined
  >(undefined);

  const {
    templates,
    isLoading,
    searchQuery,
    handleSearchChange,
    getTemplateById,
    defaultTemplate,
  } = useMeetingTemplates();

  const defaultTemplateId = defaultTemplate?.id;

  const {
    data: templateData,
    refetch: refetchMeetingTemplate,
    isLoading: isLoadingMeetingTemplate,
  } = useReactQuery({
    action: {
      key: [QueryKeys.getMeeting, editId],
      apiFunc: () => getMeetingTemplate(editId),
    },
    config: {
      enabled: false,
    },
  });

  const { mutate: updateTemplate, isPending } = useReactMutation({
    apiFunc: () => putMeetingTemplate(editId),
    onSuccess: () => {
      refetchMeetingTemplate();
      if (!editId) {
        setShowForm(false);
      }
    },
  });

  const { mutate: setDefaultTemplate, isPending: isLoadingSetDefault } =
    useReactMutation({
      apiFunc: () => setDefaultMeetingTemplate(editId),
      onSuccess: () => {
        refetchMeetingTemplate();
        if (!editId) {
          setShowForm(false);
        }
      },
    });

  const { mutate: deleteDefaultTemplate, isPending: isLoadingDelDefault } =
    useReactMutation({
      apiFunc: () => deleteDefaultMeetingTemplate(editId),
      onSuccess: () => {
        refetchMeetingTemplate();
        if (!editId) {
          setShowForm(false);
        }
      },
    });

  const onSetDefaultTemplate = (isDefault?: boolean, templateId: string) => {
    if (isDefault) {
      deleteDefaultTemplate(templateId);
    } else {
      setDefaultTemplate(templateId);
    }
  };

  const isLoadingDefault = isLoadingDelDefault || isLoadingSetDefault;

  const formData = {
    templateName: templateData?.title,
    subject: templateData?.subject,
    message: templateData?.message,
  };

  const handleTemplateClick = (templateId: string) => {
    const value = getTemplateById(templateId);
    if (value) {
      setEditId(templateId);
      setShowForm(true);
      setTimeout(() => {
        refetchMeetingTemplate();
      }, 0);
    }
  };

  const normalizedTemplates = templates.map(
    transformMeetingTemplateToNormalized
  );

  const handleClose = () => {
    if (showForm) {
      if (editId) setEditId('');
      setShowForm(false);
    } else {
      handleChangeParams({
        remove: ['chooseTemplate'],
      });
    }
  };

  const handleFormDiscard = () => {
    setShowForm(false);
  };

  const onSubmit = () => {
    updateTemplate({
      title: editedValue?.title,
      subject: editedValue?.subject,
      message: editedValue?.message,
    });
  };

  return (
    <FixedRightSideModalDialog
      onClose={handleClose}
      onClickOutside={handleClose}
      isOpenAnimation
      wide
      modalClassName="overflow-hidden"
      contentClassName="overflow-hidden"
      modalDialogClassName="overflow-hidden"
    >
      <ModalHeaderSimple
        title={t('meeting_templates')}
        backButtonProps={{
          onClick: handleClose,
        }}
        noCloseButton
        hideBack={false}
      />

      <ModalBody>
        {showForm ? (
          <EnhancedTemplateForm
            formData={formData}
            isLoading={isLoadingMeetingTemplate}
            isUpdatingDefault={isLoadingDefault}
            onSetDefault={() =>
              onSetDefaultTemplate(templateData?.default, editId)
            }
            isDefaultTemplate={templateData?.default}
            onChange={(data) =>
              setEditedValue({
                title: data?.templateName,
                subject: data?.subject,
                message: data?.message,
              })
            }
            config={{
              showDelay: false,
              showFollowup: false,
              showAttachments: false,
            }}
          />
        ) : (
          <Flex className="flex-1 overflow-hidden">
            <TemplateList
              templates={normalizedTemplates}
              isLoading={isLoading}
              searchQuery={searchQuery}
              onSearchChange={handleSearchChange}
              onSetDefault={(templateId) =>
                onSetDefaultTemplate(defaultTemplateId, templateId)
              }
              onTemplateClick={handleTemplateClick}
              defaultTemplateId={defaultTemplateId}
              config={{
                showSearch: true,
                showActions: false,
                showDefaultToggle: true,
                emptyStateMessage: t('no_meeting_templates_found'),
              }}
            />
          </Flex>
        )}
      </ModalBody>

      <ModalFooter>
        {showForm ? (
          <Flex flexDir="row" className="flex-row gap-4 flex-shrink-0">
            <Button
              label={t('discard')}
              schema="gray"
              variant="default"
              onClick={handleFormDiscard}
              className="flex-1"
            />
            <Button
              label={editId ? t('update') : t('create')}
              schema="primary-blue"
              variant="default"
              onClick={onSubmit}
              className="flex-1"
              disabled={isPending}
              isLoading={isPending}
            />
          </Flex>
        ) : (
          <Button
            fullWidth
            label={t('create_template')}
            leftIcon="plus"
            leftType="fas"
            schema="semi-transparent"
            variant="default"
            onClick={() => setShowForm(true)}
          />
        )}
      </ModalFooter>
    </FixedRightSideModalDialog>
  );
};

export default TemplateSelection;
