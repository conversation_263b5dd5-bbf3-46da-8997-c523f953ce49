import { useSearchParams } from 'next/navigation';
import { useGetAllProviders } from '@shared/components/molecules/EventsIntegration/utils/useGetAllProviders';
import useGetPrivateFileDetails from '@shared/hooks/api-hook/useGetPrivateFileDetails';
import { useSchedulesUrlState } from '@shared/hooks/useSchedulesUrlState';
import { createCandidateMeeting } from '@shared/utils/api/candidates';
import { linkValidationNonNullable } from '@shared/utils/form/formValidator/customValidations/linkValidation';
import { useGetNormalizedArrayQuery } from '@shared/utils/hooks/useGetNormalizedArrayQuery';
import {
  ConferenceProviderName,
  ProviderType,
} from 'shared/components/molecules/EventsIntegration/utils/type';
import useGetCurrentTimeZone from 'shared/hooks/api-hook/useGetCurrentTimeZone';
import useToast from 'shared/uikit/Toast/useToast';
import schedulesApi from 'shared/utils/api/schedules';
import { schedulesDb, schedulesEventTypes } from 'shared/utils/constants/enums';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useUpdateQueryData from 'shared/utils/hooks/useUpdateQueryData';
import {
  meetingDurations,
  meetingReminders,
} from 'shared/utils/normalizers/schedules';
import { BE_TIME_FORMAT, dayjs, Time } from 'shared/utils/Time';
import useSchedulesEvent from 'shared/hooks/useSchedulesEvent';
import getSchedulesSectionsQueryKey from 'shared/utils/getSchedulesSectionsQueryKey';
import { useSetEvents } from 'shared/hooks/useSetEvents';
import type {
  BEMeetingDetails,
  BEReminderDetails,
  BETaskDetails,
  CreatableSchedulesEventTypes,
  ICreateMeetingData,
  MeetingDetails,
} from 'shared/types/schedules/schedules';
import type { UploadedFile } from 'shared/uikit/AttachmentPicker/AttachmentPicker.component';
import { MeetingChannel, TaskStatus } from 'shared/types/schedules/schedules';
import { useScheduleFormFieldOptions } from 'shared/hooks/schedules/useScheduleFormFieldOptions';
import { meetingCreatorAttendeePermissions } from 'shared/constants/schedules';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import formValidator, {
  endDateAfterStartCB,
  isEmptyValidator,
  timeValidator,
  noTimeWithoutDate,
} from 'shared/utils/form/formValidator';
import useMeetingTemplates from '@shared/hooks/schedules/useMeetingTemplates';

const useScheduleForm = <T extends CreatableSchedulesEventTypes>() => {
  const {
    schedulesEventType,
    event,
    isCreationMode,
    backHandler,
    closeHandler,
    queryResult,
    creationInitialData,
  } = useSchedulesEvent<T>();
  const { state } = useSchedulesUrlState();
  const getQueryValue = useGetNormalizedArrayQuery();
  const { isInCandidateManager } = state.scheduleEventsPanelData || {};
  const { t } = useTranslation();
  const toast = useToast();
  const { data: currentTimeZone = {} as any } = useGetCurrentTimeZone();
  const { meetingQueryKey } = getSchedulesSectionsQueryKey(schedulesEventType);
  const { refetch: refetchMeetings } = useUpdateQueryData(meetingQueryKey);
  const { fetchEvents } = useSetEvents();
  const { authUser } = useGetAppObject();
  const currentEntityId = getQueryValue('currentEntityId');
  const {
    assigneePermissionOption,
    taskStatusOptions,
    attendeePermissionsOptions,
  } = useScheduleFormFieldOptions();
  const searchParams = useSearchParams();
  const isCandidateMode = searchParams?.get('isCandidateMode');

  const defaultTimeZone = {
    ...currentTimeZone,
    value: currentTimeZone?.id,
  };

  const { defaultTemplate } = useMeetingTemplates();

  const initialValues = {
    [schedulesEventTypes.MEETING]: {
      timezone: defaultTimeZone,
      contactType: schedulesDb.contactType[0],
      meetingChannel: MeetingChannel.LOBOX_ACCOUNT,
      attendeePermissions: [],
      permissions: { ...meetingCreatorAttendeePermissions },
      attachmentFileIds: [],
      attendees: [],
      duration: meetingDurations._30_MINUTES,
      remind: meetingReminders._15_MIN_BEFORE,
      creator: authUser,
      currentUserIsCreator: true,
      description: defaultTemplate?.message || '',
      ...creationInitialData,
    } as Partial<MeetingDetails>,
    [schedulesEventTypes.REMINDER]: { ...creationInitialData },
    [schedulesEventTypes.TASK]: {
      status: {
        label: t('open'),
        value: TaskStatus.OPEN,
      },
      ...creationInitialData,
    },
  };

  const { data: attachmentFiles = [], refetch: refetchAttachmentFiles } =
    useGetPrivateFileDetails(event?.attachmentFileIds, ['event', event?.id], {
      enabled: !!event?.attachmentFileIds?.length,
    });

  const { data: externalConferenceProvider, refetch: refetchProviders } =
    useGetAllProviders(ProviderType.Conference, {
      enabled:
        event &&
        'externalConferenceProviderType' in event &&
        !!event.externalConferenceProviderType &&
        event.externalConferenceProviderType !== 'LOBOX' &&
        !!event.externalConferenceProviderId,
      select(data) {
        if (isCreationMode) return undefined;
        if (
          event &&
          'externalConferenceProviderType' in event &&
          !!event.externalConferenceProviderType
        ) {
          const currentProviderItem = data?.[
            event.externalConferenceProviderType
          ]?.find(
            ({ id }) => id === String(event.externalConferenceProviderId)
          );
          if (currentProviderItem)
            return {
              label: currentProviderItem?.externalUserName || '',
              value: currentProviderItem,
            };
        }

        return undefined;
      },
    });

  const editInitialValues = event && {
    ...event,
    assigneePermission:
      'assigneePermission' in event && event.assigneePermission
        ? assigneePermissionOption
        : [],
    attendeePermissions:
      'attendees' in event
        ? attendeePermissionsOptions.filter((permissionOption) =>
            event.attendees?.[0]?.permissions.includes(permissionOption.value)
          )
        : [],
    assignee: 'assignee' in event ? event.assignee.user : undefined,
    status:
      'status' in event &&
      taskStatusOptions.find((option) => option.value === event.status),
    startTime: Time.getFormTime(
      Time.getLocalDateFromUTCStr(
        event?.start,
        'timezone' in event ? (event.timezone?.code ?? undefined) : undefined
      )
    ),
    startDate: Time.getFormDate(
      Time.getLocalDateFromUTCStr(
        event?.start,
        'timezone' in event ? (event.timezone?.code ?? undefined) : undefined
      )
    ),
    endTime:
      'end' in event
        ? Time.getFormTime(Time.getLocalDateFromUTCStr(event?.end))
        : undefined,
    endDate:
      'end' in event
        ? Time.getFormDate(Time.getLocalDateFromUTCStr(event?.end))
        : undefined,
    attachmentFiles,
    externalConferenceProvider,
  };

  const apiFunc = {
    [schedulesEventTypes.MEETING]: isCreationMode
      ? isInCandidateManager
        ? (data: ICreateMeetingData) =>
            createCandidateMeeting(currentEntityId, data, isCandidateMode)
        : schedulesApi.createMeeting
      : schedulesApi.updateMeeting,
    [schedulesEventTypes.REMINDER]: isCreationMode
      ? schedulesApi.createReminder
      : schedulesApi.updateReminder,
    [schedulesEventTypes.TASK]: isCreationMode
      ? schedulesApi.createTask
      : schedulesApi.updateTask,
  };

  const transform = {
    [schedulesEventTypes.MEETING]: ({
      duration,
      remind,
      externalConferenceProvider,
      externalConferenceProviderType,
      startTime,
      timezone,
      startDate,
      attendees,
      attachmentFiles,
      attendeePermissions,
      contactType,
      location,
      meetingChannel,
      customLink,
      ...rest
    }: any): BEMeetingDetails => ({
      ...rest,
      // attachmentFileIds,
      attendees: attendees?.map((attendee: any) => ({
        ...(attendee.userId
          ? {
              id: attendee.id,
              userId: attendee.userId,
            }
          : {
              userId: attendee.id,
            }),
        permissions: attendeePermissions.map(
          (permission: any) => permission.value
        ),
      })),
      duration: duration?.value,
      contactType: contactType?.value,
      remind: remind?.value,
      start: Time.getBackendDateTimeFromDateAndTime(
        startDate,
        startTime?.value,
        Number(timezone?.offset) || 0
      ),
      timezoneCode: timezone?.code,
      timezoneId: timezone?.value,
      timezoneLabel: timezone?.label,
      timezoneOffset: timezone?.offset,
      attachmentFileIds: attachmentFiles?.map((item: UploadedFile) =>
        Number(item?.id || 0)
      ),
      location: location?.[0]?.location,
      ...(meetingChannel === MeetingChannel.LOBOX_ACCOUNT
        ? {
            externalConferenceProviderType: ConferenceProviderName.LOBOX,
          }
        : meetingChannel === MeetingChannel.PERSONAL_ACCOUNT
          ? {
              externalConferenceProviderType:
                externalConferenceProvider?.value?.type,
              externalConferenceProviderId:
                externalConferenceProvider?.value?.id,
            }
          : {
              customLink,
            }),
    }),
    [schedulesEventTypes.REMINDER]: ({
      repeatType,
      startTime,
      startDate,
      allDay,
      ...rest
    }: any): BEReminderDetails => ({
      ...rest,
      repeatType: repeatType?.value,
      allDay: allDay || false,
      datetime: allDay
        ? dayjs(startDate).tz('UTC').format(BE_TIME_FORMAT)
        : Time.getBackendDateTimeFromDateAndTime(startDate, startTime?.value),
    }),
    [schedulesEventTypes.TASK]: ({
      startTime,
      endTime,
      // category,
      assignee,
      startDate,
      endDate,
      attachmentFiles,
      assigneePermission,
      status,
      ...rest
    }: any): BETaskDetails => ({
      ...rest,
      assignee: assignee
        ? {
            userId: assignee.id,
            modifyPermission: Boolean(assigneePermission?.[0]?.value),
          }
        : undefined,
      start: Time.getBackendDateTimeFromDateAndTime(
        startDate,
        startTime?.value
      ),
      end: Time.getBackendDateTimeFromDateAndTime(endDate, endTime?.value),
      attachmentFileIds: attachmentFiles?.map((item: UploadedFile) => item?.id),
      status: status.value,
    }),
  };
  const messages = {
    [schedulesEventTypes.MEETING]: isCreationMode
      ? t('meeting_add_success')
      : t('meeting_updated_success'),
    [schedulesEventTypes.REMINDER]: isCreationMode
      ? t('reminder_add_success')
      : t('reminder_updated_success'),
    [schedulesEventTypes.TASK]: isCreationMode
      ? t('task_add_success')
      : t('task_updated_success'),
  };

  const validationSchemas = {
    [schedulesEventTypes.MEETING]: formValidator.object().shape({
      startTime: timeValidator(),
      title: isEmptyValidator('meeting_title_required'),
      customLink: linkValidationNonNullable,
    }),
    [schedulesEventTypes.REMINDER]: formValidator.object().shape({
      startTime: timeValidator(),
    }),
    [schedulesEventTypes.TASK]: formValidator
      .object()
      .shape({
        startTime: timeValidator(),
        endTime: timeValidator({ hasAllDay: true }),
      })
      .test(
        'no-time-without-date',
        '',
        noTimeWithoutDate({
          dateFieldName: 'endDate',
          timeFieldName: 'endTime',
          dateErrorMessage: t('should_set_time'),
          timeErrorMessage: t('should_set_date_first'),
        })
      )
      .test(
        'endDate is after startDate',
        '',
        endDateAfterStartCB({ errorMessage: t('end_after_start') })
      ),
  };

  const onSuccessHandler = () => {
    refetchMeetings();
    toast({
      type: 'success',
      icon: 'check-circle',
      message: messages[schedulesEventType],
    });
    if (isCreationMode) {
      closeHandler();
    } else {
      queryResult.refetch();
      refetchAttachmentFiles();
      refetchProviders();
      backHandler();
    }
    fetchEvents();
  };

  return {
    initialValues: isCreationMode
      ? initialValues[schedulesEventType]
      : editInitialValues,
    apiFunc: apiFunc[schedulesEventType],
    transform: transform[schedulesEventType],
    validationSchema: validationSchemas[schedulesEventType],
    onSuccessHandler,
  };
};

export default useScheduleForm;
