import { useMemo } from 'react';
import TemplateSelection from '@shared/components/Organism/TemplateSelectionInterface/TemplateSelectionInterface';
import { useSchedulesUrlState } from '@shared/hooks/useSchedulesUrlState';
import { isBusinessApp } from '@shared/utils/getAppEnv';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import Form from 'shared/uikit/Form';
import { AttendeePickerPanel } from './AttendeePickerPanel';
import useScheduleForm from './partials/useScheduleForm';
import ScheduleCreationFormContent from './ScheduleCreationForm.content';

const ScheduleCreationForm = () => {
  const {
    initialValues,
    apiFunc,
    transform,
    onSuccessHandler,
    validationSchema,
  } = useScheduleForm();

  const { state } = useSchedulesUrlState();
  const { allParams } = useCustomParams();

  const { isInAttendeeSelection, schedulesEventType } = useMemo(
    () => ({
      isInAttendeeSelection:
        state?.scheduleEventsPanelData?.isInAttendeeSelection,
      schedulesEventType: state?.scheduleEventsPanelData?.schedulesEventType,
    }),
    [state]
  );

  const isTemplateSelectionActive = allParams.chooseTemplate === 'true';

  if (!schedulesEventType) return null;

  if (isTemplateSelectionActive) {
    return <TemplateSelection />;
  }

  return (
    <Form
      key={schedulesEventType}
      initialValues={initialValues}
      apiFunc={apiFunc}
      onSuccess={onSuccessHandler}
      transform={transform}
      enableReinitialize
      validationSchema={validationSchema}
    >
      <ScheduleCreationFormContent />
      {isInAttendeeSelection && (
        <AttendeePickerPanel modalProps={{ wide: isBusinessApp }} />
      )}
    </Form>
  );
};

export default ScheduleCreationForm;
