import React, { useEffect, useCallback } from 'react';
import BaseButton from '@shared/uikit/Button/BaseButton';
import Flex from '@shared/uikit/Flex';
import Form from '@shared/uikit/Form';
import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import Icon from '@shared/uikit/Icon';
import Skeleton from '@shared/uikit/Skeleton';
import Spinner from '@shared/uikit/Spinner';
import Tooltip from '@shared/uikit/Tooltip';
import Typography from '@shared/uikit/Typography';
import cnj from '@shared/uikit/utils/cnj';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type {
  TemplateFormData,
  TemplateFormConfig,
} from '../types/template.types';
import type { FormikProps } from 'formik';

interface EnhancedTemplateFormProps {
  formData: TemplateFormData;
  onSubmit: (data: TemplateFormData) => void;
  isLoading?: boolean;
  onChange?: (values: TemplateFormData, isValid: boolean) => void;
  config?: TemplateFormConfig;
  isDefaultTemplate?: boolean;
  isUpdatingDefault?: boolean;
  onSetDefault: () => void;
}

const FormContent = ({
  handleChange,
  groups,
  props,
}: {
  handleChange: (values: TemplateFormData, isValid: boolean) => void;
  props: FormikProps<TemplateFormData>;
  groups: (props: any) => any[];
}) => {
  useEffect(() => {
    handleChange(props.values, props.isValid);
  }, [props.values, props.isValid]);

  return (
    <Flex className="w-full">
      <DynamicFormBuilder groups={groups(props)} />
    </Flex>
  );
};

const EnhancedTemplateForm: React.FC<EnhancedTemplateFormProps> = ({
  formData,
  onSubmit,
  isLoading = false,
  isDefaultTemplate = false,
  isUpdatingDefault,
  onSetDefault,
  onChange,
  config = {},
}) => {
  const { t } = useTranslation();

  const {
    showDelay = true,
    showFollowup = true,
    showAttachments = true,
    readOnly = false,
    customFields = [],
  } = config;

  const delayOptions = [
    { label: t('immediately'), value: 'IMMEDIATELY' },
    { label: t('10_minutes'), value: '10_MINUTES' },
    { label: t('30_minutes'), value: '30_MINUTES' },
    { label: t('2_hours'), value: '2_HOURS' },
    { label: t('5_hours'), value: '5_HOURS' },
    { label: t('10_hours'), value: '10_HOURS' },
    { label: t('12_hours'), value: '12_HOURS' },
    { label: t('24_hours'), value: '24_HOURS' },
    { label: t('48_hours'), value: '48_HOURS' },
  ];

  console.log('new changes', formData);

  const followupPeriodOptions = [
    { label: t('3_days'), value: '_3_DAYS' },
    { label: t('5_days'), value: '_5_DAYS' },
    { label: t('7_days'), value: '_7_DAYS' },
    { label: t('14_days'), value: '_14_DAYS' },
  ];

  const handleChange = useCallback(
    (values: TemplateFormData, isValid: boolean) => {
      onChange?.(values, isValid);
    },
    [onChange]
  );

  const groups = (props: any) => {
    const baseGroups = [];

    if (showDelay) {
      baseGroups.push({
        name: 'delay',
        cp: 'dropdownSelect',
        label: t('delay'),
        options: delayOptions,
        value: delayOptions.find((item) => item.value === formData?.delay),
        required: true,
        wrapStyle: '!mb-8',
        disabled: readOnly,
      });
    }

    baseGroups.push(
      {
        name: 'templateName',
        cp: 'input',
        label: t('template_name'),
        required: true,
        wrapStyle: '!mb-8',
        disabled: readOnly,
        rightIcon: (
          <BaseButton
            onClick={(e) => {
              e.stopPropagation();
              onSetDefault();
            }}
            disabled={isUpdatingDefault}
          >
            {isUpdatingDefault ? (
              <Spinner size={12} color="primary-blue" />
            ) : (
              <Typography
                className={cnj(
                  'text-sm font-medium text-primaryText',
                  isDefaultTemplate ? 'text-success' : 'text-primaryText'
                )}
              >
                {isDefaultTemplate ? t('default') : t('set_as_default')}
              </Typography>
            )}
          </BaseButton>
        ),
      },
      {
        name: 'subject',
        cp: 'input',
        label: t('subject'),
        required: true,
        wrapStyle: '!mb-8',
        disabled: readOnly,
      },
      {
        name: 'message',
        cp: 'richtext',
        label: t('message'),
        required: true,
        variant: 'form-input',
        maxLength: 80000,
        helperText: t('use_brackets_for_dynamic_data'),
        className: '!min-h-[120px] bg-popOverBg_white',
        wrapStyle: '!mb-8',
        disabled: readOnly,
      }
    );

    if (showAttachments) {
      baseGroups.push({
        name: 'attachments',
        cp: 'attachmentPicker',
        label: t('attachments'),
        required: false,
        wrapStyle: '!mb-8',
        disabled: readOnly,
      });
    }

    if (showFollowup) {
      baseGroups.push({
        name: 'hasFollowup',
        cp: 'checkBox',
        label: t('have_follow_up_message'),
        required: false,
        wrapStyle: '!mb-8',
        visibleOptionalLabel: false,
        disabled: readOnly,
        labelProps: {
          size: 15,
          font: '500',
          color: 'primaryText',
        },
        rightComponent: (
          <Tooltip
            placement="top"
            trigger={
              <Icon
                color="secondaryDisabledText"
                type="fal"
                name="info-circle"
                size={15}
              />
            }
          >
            {t('send_a_follow-up')}
          </Tooltip>
        ),
      });

      if (props.values.hasFollowup) {
        baseGroups.push(
          {
            name: 'followupPeriod',
            cp: 'dropdownSelect',
            label: t('follow_up_after'),
            options: followupPeriodOptions,
            value: followupPeriodOptions.find(
              (item) => item.value === formData?.followupPeriod
            ),
            required: false,
            visibleOptionalLabel: false,
            wrapStyle: '!mb-8',
            disabled: readOnly,
          },
          {
            name: 'followupTitle',
            cp: 'input',
            label: t('message_title'),
            required: false,
            wrapStyle: '!mb-8',
            visibleOptionalLabel: false,
            disabled: readOnly,
          },
          {
            name: 'followupMessage',
            cp: 'richtext',
            label: t('message'),
            required: false,
            variant: 'form-input',
            maxLength: 80000,
            helperText: t('use_brackets_for_dynamic_data'),
            className: '!min-h-[120px] bg-popOverBg_white',
            wrapStyle: '!mb-8',
            visibleOptionalLabel: false,
            disabled: readOnly,
          },
          {
            name: 'followupAttachments',
            cp: 'attachmentPicker',
            label: t('attachments'),
            required: false,
            wrapStyle: '!mb-8',
            visibleOptionalLabel: false,
            disabled: readOnly,
          }
        );
      }
    }

    baseGroups.push(...customFields);

    return baseGroups;
  };

  return (
    <Flex className="w-full p-20 rounded-xl bg-gray_5">
      {isLoading ? (
        <Flex className="w-full">
          <Skeleton
            style={{
              width: '100%',
              height: 40,
              borderRadius: 4,
              marginBottom: 16,
            }}
          />
        </Flex>
      ) : (
        <Form
          initialValues={formData}
          onSuccess={onSubmit}
          enableReinitialize
          validateOnChange
        >
          {(props) => (
            <FormContent
              groups={groups}
              handleChange={handleChange}
              props={props}
            />
          )}
        </Form>
      )}
    </Flex>
  );
};

export default EnhancedTemplateForm;
