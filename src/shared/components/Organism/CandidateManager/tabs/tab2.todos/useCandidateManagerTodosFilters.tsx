import { useMemo } from 'react';
import { noteVisibilityFilterOptions } from '@shared/utils/constants/enums/candidateDb';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { usePanelFilter } from '../../hooks/usePanelFilter';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import { getCandidatesTodosFilters } from '@shared/utils/api/candidates/todos';
import { QueryKeys } from '@shared/utils/constants';
import { useManagerContext } from '@shared/components/Organism/CandidateManager/CandidateManager.context';
import { ORIGINAL_CANDIDATE } from '@shared/components/Organism/CandidateManager/contants';

export default function useCandidateManagerTodosFilters() {
  const { t } = useTranslation();
  const { selectedSummary } = useManagerContext();

  const { data } = useReactQuery({
    action: {
      apiFunc: getCandidatesTodosFilters,
      key: [QueryKeys.candidateTodosFilters],
      params: {
        isCandidateMode: selectedSummary?.type === ORIGINAL_CANDIDATE,
      },
    },
  });

  const {
    filters: { visibility, creatorIds, assignees },
  } = usePanelFilter('notes');

  const VISIBILITY = useMemo(
    () => ({
      formGroup: {
        color: 'smoke_coal',
        title: t('visibility'),
      },
      label: t('visibility'),
      cp: 'radioGroup',
      name: 'visibility',
      divider: {
        className: 'my-20',
      },
      options: noteVisibilityFilterOptions,
      getValue: () => visibility || 'ALL',
      schema: 'semi-transparent',
      isDefaultValue: !visibility || visibility === 'ALL',
    }),
    [t, visibility]
  );

  const CREATED_BY = useMemo(
    () => ({
      formGroup: {
        color: 'smoke_coal',
        title: t('created_by'),
      },
      cp: 'checkBoxGroup',
      label: t('created_by'),
      withConfirmation: false,
      name: 'creatorIds',
      options: [],
      getValue: () => creatorIds || [],
      divider: {
        className: 'my-20',
      },
    }),
    [creatorIds, t]
  );

  const ASSIGNEES = useMemo(
    () => ({
      formGroup: {
        color: 'smoke_coal',
        title: t('assignees'),
      },
      cp: 'checkBoxGroup',
      label: t('assignees'),
      withConfirmation: false,
      name: 'assignees',
      options: [],
      getValue: () => assignees || [],
      divider: {
        className: 'my-20',
      },
    }),
    [assignees, t]
  );

  return useMemo(
    () => [VISIBILITY, CREATED_BY, ASSIGNEES],
    [VISIBILITY, CREATED_BY, ASSIGNEES]
  );
}
