import useReactInfiniteQuery from '@shared/utils/hooks/useInfiniteQuery';
import { useQueryClient } from '@tanstack/react-query';
import type { FormikProps } from 'formik';
import { useFormikContext } from 'formik';
import { useCallback, useMemo, useState } from 'react';
import MultipleInput from '@shared/components/molecules/MultipleInput';
import TodoItemBody from '@shared/components/molecules/TodoItem/TodoItemBody';
import TodoItemSkeleton from '@shared/components/molecules/TodoItem/TodoItemSkeleton';
import CandidateTodoCard from '@shared/components/Organism/CandidateTodoCard';
import { getTodoValidationSchema } from '@shared/components/Organism/CandidateTodoCard/CandidateTodoCard.component';
import { useTodoFields } from '@shared/components/Organism/CandidateTodoCard/hooks/useTodoFields';
import EmptySearchResult from '@shared/components/Organism/EmptySearchResult';
import useGetAppObject from '@shared/hooks/useGetAppObject';
import Flex from '@shared/uikit/Flex';
import Form from '@shared/uikit/Form';
import cnj from '@shared/uikit/utils/cnj';
import * as API from '@shared/utils/api/candidates';
import { QueryKeys } from '@shared/utils/constants';
import classes from '../tab.module.scss';
import useReactMutation from '@shared/utils/hooks/useReactMutation';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { useManagerContext } from '../../CandidateManager.context';
import Filterbar from '../../components/Filterbar';
import { FooterWrap } from '../../components/FooterWrap';
import { usePanelFilter } from '../../hooks/usePanelFilter';
import type {
  CandidateFormData,
  CandidateTodoRequest,
  ICandidateTodo,
} from '@shared/types/candidates';
import { ORIGINAL_CANDIDATE } from '@shared/components/Organism/CandidateManager/contants';

interface CandidateTodosManagerProps {
  candidate: CandidateFormData;
}

export function CandidateTodosManager({
  candidate,
}: CandidateTodosManagerProps) {
  const [formKey, setFormKey] = useState(0);
  const { selectedSummary } = useManagerContext();
  const isCandidateMode = selectedSummary?.type === ORIGINAL_CANDIDATE;
  const { filters } = usePanelFilter('todos');
  const { t } = useTranslation();
  const { authUser } = useGetAppObject();
  const queryClient = useQueryClient();
  const { data, isFetching, refetch } = useReactInfiniteQuery<ICandidateTodo>(
    [QueryKeys.candidateTodos, selectedSummary, filters],
    {
      func: API.getCandidatesTodosSearch,
      extraProps: {
        isCandidateMode,
        candidateId: isCandidateMode ? selectedSummary?.candidateId : undefined,
        participationId: !isCandidateMode ? selectedSummary?.id : undefined,
        ...filters,
      },
      size: 10,
    },
    {
      enabled: !!selectedSummary,
    }
  );

  const onSuccess = useCallback(
    (
      apiResponse: any,
      values?: any,
      formikRef?: FormikProps<ICandidateTodo>
    ) => {
      setTimeout(() => {
        refetch();
      }, 300);

      formikRef?.resetForm();
      setFormKey((k) => k + 1);
    },
    [queryClient]
  );

  const { mutate: addTodo } = useReactMutation({
    apiFunc: API.addCandidateTodo,
  });

  const apiFunc = (data: CandidateTodoRequest) => {
    addTodo({
      id: isCandidateMode ? selectedSummary?.candidateId : selectedSummary?.id,
      isCandidateMode,
      body: data,
    });
  };

  const validationSchema = useMemo(getTodoValidationSchema, []);

  const freshTodo: Partial<ICandidateTodo> = {
    title: '',
    description: '<p></p>',
    assigneeUser: authUser,
  };

  return (
    <>
      <Flex className={cnj('sticky top-0 w-full', classes.scrollArea)}>
        <Filterbar rootName="todos" />
      </Flex>
      <Flex className={cnj('flex-1 !pt-0', classes.scrollArea)}>
        {isFetching ? (
          <TodoItemSkeleton
            displayCreator
            classNames={{
              root: 'border border-solid !border-techGray_20 bg-background',
              container: '!p-12',
            }}
          />
        ) : data.length ? (
          data.map((todo) => (
            <CandidateTodoCard
              key={todo.id}
              item={todo}
              candidateId={candidate.id}
            />
          ))
        ) : (
          <EmptySearchResult
            className="h-full w-full items-center"
            title={t('no_todos_found')}
            sectionMessage={t('no_todos_found_desc')}
          />
        )}
      </Flex>
      <Form
        key={formKey}
        onSuccess={onSuccess}
        apiFunc={apiFunc}
        initialValues={freshTodo}
        validationSchema={validationSchema}
        transform={newCandidateTodoTransform}
      >
        <FooterWrap>
          <TodoMultipleInput />
        </FooterWrap>
      </Form>
    </>
  );
}

function TodoMultipleInput() {
  const fields = useTodoFields();
  const { values } = useFormikContext<ICandidateTodo>();
  const expand =
    !!values.title ||
    !!values.start ||
    !!values.end ||
    (!!values.description && values.description !== '<p></p>');
  const groups = useMemo(
    () =>
      [
        fields.TITLE_COMPOSE,
        expand ? fields.DESCRIPTION_COMPOSE : undefined,
        fields.ASSIGNEE_POPPER,
        fields.DATES_POPPER,
      ].filter(Boolean),
    [fields, expand]
  );

  return (
    <MultipleInput showAuthorAvatar groups={groups} expand={expand}>
      {expand && <TodoItemPreview />}
    </MultipleInput>
  );
}

export function TodoItemPreview() {
  const { values } = useFormikContext<ICandidateTodo>();

  return <TodoItemBody item={values} variant="preview" />;
}

export function newCandidateTodoTransform(
  data: ICandidateTodo
): CandidateTodoRequest {
  return {
    title: data.title,
    description: data.description,
    assigneeUserId: +data.assigneeUser!.id,
    start: data.start,
    end: data.end,
  };
}
