import Flex from '@shared/uikit/Flex';
import Icon from '@shared/uikit/Icon';
import Typography from '@shared/uikit/Typography';
import { getUpcomingMeetings } from '@shared/utils/api/candidates';
import { QueryKeys } from '@shared/utils/constants';
import useReactInfiniteQuery from '@shared/utils/hooks/useInfiniteQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import { useManagerContext } from '../CandidateManager.context';
import classes from './UpcomingMeetingsNotice.module.scss';
import type { FC } from 'react';
import { ORIGINAL_CANDIDATE } from '@shared/components/Organism/CandidateManager/contants';

export const UpcomingMeetingsNotice: FC = () => {
  const { t } = useTranslation();
  const { candidate, setSelectedTab, selectedSummary } = useManagerContext();
  const isCandidateMode = selectedSummary?.type === ORIGINAL_CANDIDATE;

  const infiniteQuery = useReactInfiniteQuery(
    [QueryKeys.candidateMeetings, candidate?.id],
    {
      func: (props: { pageParam?: number }) =>
        getUpcomingMeetings({
          params: {
            candidate: candidate?.id ?? '',
            page: props.pageParam,
            size: 10,
            isCandidateMode,
          },
        }),
      size: 10,
    },
    {
      enabled: !!candidate?.id,
      refetchOnWindowFocus: false,
    }
  );

  const handleViewDetails = () => {
    setSelectedTab('meetings');
  };

  if (!infiniteQuery.data?.length) return null;

  return (
    <Flex className={classes.upcomingMeetingsNotice}>
      <Flex flexDir="row" className={classes.upcomingMeetingsNotice__content}>
        <Icon color="success" name="meeting" size={24} type="far" />
        <Typography
          className={classes['upcomingMeetingsNotice__content--text']}
        >
          {translateReplacer(t('upcoming_meetings_notice'), [
            infiniteQuery.data?.length.toString(),
          ])}
        </Typography>
      </Flex>
      <Typography
        onClick={handleViewDetails}
        className={classes.upcomingMeetingsNotice__link}
      >
        {t('view_details')}
      </Typography>
    </Flex>
  );
};
