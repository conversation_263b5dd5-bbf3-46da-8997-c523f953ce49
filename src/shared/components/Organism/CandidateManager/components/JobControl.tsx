import { useEffect, type FC } from 'react';
import { ORIGINAL_CANDIDATE } from '@shared/components/Organism/CandidateManager/contants';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import { openMultiStepForm } from '@shared/hooks/useMultiStepForm';
import DropdownSelect from '@shared/uikit/AutoComplete/DropdownSelect';
import Button from '@shared/uikit/Button';
import BaseButton from '@shared/uikit/Button/BaseButton';
import DividerVertical from '@shared/uikit/Divider/DividerVertical';
import Flex from '@shared/uikit/Flex';
import Skeleton from '@shared/uikit/Skeleton';
import cnj from '@shared/uikit/utils/cnj';
import { getCandidateSummary } from '@shared/utils/api/candidates';
import { QueryKeys } from '@shared/utils/constants/enums';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { useManagerContext } from '../CandidateManager.context';
import classes from './JobControl.module.scss';
import { SummaryItem } from './SummaryItem';
import type { ICandidateSummaryOption } from '@shared/types/candidates';

interface Props {
  className?: string;
}

export const CandidateManagerJobControl: FC<Props> = ({ className }) => {
  const { t } = useTranslation();
  const dispatch = useGlobalDispatch();
  const { handleChangeParams } = useCustomParams();

  const { candidate, setSelectedSummary, selectedSummary, isCandidate } =
    useManagerContext();
  const candidateId = candidate?.id;

  const isApplicant = !candidate?.isLoboxCandidate;
  const participationId = candidate?.id;

  const handleRejectClick = () => {
    dispatch({ type: 'TOGGLE_CANDIDATE_MANAGER', payload: { isOpen: false } });
    openMultiStepForm({
      formName: 'automation',
      data: { isApplicant: !!isApplicant, participationId },
      type: 'rejection',
    });
  };
  const addSearchParam = (key: string, value: any) => {
    handleChangeParams({ add: { [key]: value } });
  };

  const { data: summary, isLoading: isLoadingSummary } = useReactQuery({
    action: {
      apiFunc: getCandidateSummary,
      params: { id: candidateId },
      key: [QueryKeys.getCandidateSummary, candidateId],
    },
    config: {
      enabled: !!candidateId && isCandidate,
    },
  });

  useEffect(() => {
    if (summary?.length) {
      setSelectedSummary(summary[0]);
      addSearchParam(
        'isCandidateMode',
        summary?.[0]?.type === ORIGINAL_CANDIDATE
      );
    }
  }, [summary]);

  return isLoadingSummary ? (
    <Skeleton className="w-full h-[70px]" />
  ) : (
    <Flex flexDir="row" className={cnj(className, classes.wrapper)}>
      <DropdownSelect
        className={classes.formContainer}
        options={summary ?? []}
        renderItem={({ item }: { item: ICandidateSummaryOption }) => (
          <SummaryItem item={item} />
        )}
        renderContent={({ onClick }: { onClick: () => void }) =>
          selectedSummary && (
            <BaseButton onClick={onClick}>
              <SummaryItem item={selectedSummary} />
            </BaseButton>
          )
        }
        label={t('application')}
        showPreview
        optionKey={(item: ICandidateSummaryOption) => `ca_${item.id}`}
        inputWrapClassName="!border-0"
        onSelect={(item: ICandidateSummaryOption) => {
          setSelectedSummary(item);
          addSearchParam('isCandidateMode', item?.type === ORIGINAL_CANDIDATE);
        }}
      />
      <DividerVertical distance={2} height={56} />
      <Flex flexDir="row" className={classes.inner}>
        <Button disabled label={t('review')} schema="semi-transparent" />
        <Button
          disabled
          label={t('reject')}
          schema="error-semi-transparent"
          onClick={handleRejectClick}
        />
      </Flex>
    </Flex>
  );
};
