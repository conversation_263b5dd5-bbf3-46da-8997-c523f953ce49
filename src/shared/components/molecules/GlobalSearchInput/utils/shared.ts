export type ItemType =
  | 'PAGE'
  | 'PERSON'
  | 'HASHTAG'
  | 'JOB'
  | 'TEXT'
  | 'LOCATION';

export const typeOrder: Record<ItemType | 'SPECIFIC_JOB', number> = {
  PERSON: 0,
  PAGE: 1,
  SPECIFIC_JOB: 2,
  HASHTAG: 3,
  JOB: 4,
  TEXT: 5,
  LOCATION: 0,
};

export const suggestionsTypeORder: Record<ItemType | 'SPECIFIC_JOB', number> = {
  PERSON: 5,
  PAGE: 4,
  SPECIFIC_JOB: 3,
  JOB: 3,
  HASHTAG: 2,
  TEXT: 1,
  LOCATION: 0,
};

type JobItem = {
  imgUrl: string;
  label: string;
  type: 'OCCUPATION' | 'SKILL' | 'PAGE' | 'JOB';
  value: string;
  location?: string;
  locationName?: string;
  title?: string;
};
type PersonOrPageOrTextItem = Required<{
  id: string;
  type: 'PERSON' | 'PAGE' | 'TEXT';
  croppedImageUrl: string;
  title: string | null;
  fullName: string | null;
  usernameAtSign: string;
  username: string;
  name?: string;
  surname?: string;
}>;
type HashtagItem = { title: string; type: 'HASHTAG' };

export const itemNormalizers = {
  job: (items: JobItem[]) =>
    items?.map((item) => ({
      ...item,
      type: 'JOB',
      isSpecific: item?.type === 'JOB',
      subtitle: item?.subtitle || item?.location || item?.locationName,
      label: item?.label || item?.title,
      entityType: 'JOB',
      moduleType: 'JOBS',
    })),
  personOrPageOrText: (data: { content: PersonOrPageOrTextItem[] }) =>
    data?.content?.map((item) => ({
      ...item,
      value: item?.id,
      label: item?.title || item?.fullName || `${item?.name} ${item?.surname}`,
      imgUrl: item?.croppedImageUrl,
      isSpecific: item?.type !== 'TEXT',
      subtitle:
        item?.usernameAtSign || item?.username
          ? `@${item?.username}`
          : item?.locationName,
      entityType:
        item.type === 'PAGE'
          ? 'PAGE'
          : item.type === 'PERSON'
            ? 'PERSON'
            : 'TEXT',
      moduleType:
        item.type === 'PAGE'
          ? 'PAGES'
          : item.type === 'PERSON'
            ? 'PEOPLE'
            : 'GLOBAL',
    })),
  hashtag: (data: { content: HashtagItem[] }) =>
    data?.content?.map((item) => ({
      ...item,
      label: item?.title,
      entityType: 'HASHTAG',
      moduleType: 'GLOBAL',
    })),

  location: (items: any) =>
    items?.map((item: any) => ({
      ...item,
      label: item?.title || item?.name,
      secondaryTitle: item?.locationOrigin,
      value: item?.id,
      entityType: 'LOCATION',
      moduleType: 'LOCATIONS',
    })),
  project: (data) =>
    data?.content?.map((item) => ({
      ...item,
      type: 'PROJECT',
      isSpecific: true,
      value: item?.id,
      label: item.title,
      entityType: 'PROJECT',
      moduleType: 'PROJECTS',
    })),
  candidate: (data) =>
    data?.content?.map((item) => ({
      ...item,
      type: 'CANDIDATE',
      value: item?.id,
      label:
        'CANDIDATE' ||
        item?.title ||
        item?.fullName ||
        `${item?.name} ${item?.surname}`,
      imgUrl: item?.croppedImageUrl,
      isSpecific: true,
      subtitle:
        item?.usernameAtSign || item?.username
          ? `@${item?.username}`
          : item?.locationName,
      entityType: 'CANDIDATE',
      moduleType: 'CANDIDATES',
    })),
  businessJob: (data: JobItem[]) =>
    data?.content?.map((item) => ({
      ...item,
      type: 'BUSINESS_JOB',
      value: item?.id,
      isSpecific: true,
      subtitle: item?.subtitle || item?.location || item?.locationName,
      label: item?.label || item?.title,
      entityType: 'JOB',
      moduleType: 'JOBS',
    })),
  companies: (data: { content: PersonOrPageOrTextItem[] }) =>
    data?.content?.map((item) => ({
      ...item,
      type: 'COMPANIES',
      value: item?.id,
      label: item?.title,
      imgUrl: item?.croppedImageUrl,
      isSpecific: true,
      subtitle: item?.locationName || `@${item?.username}`,
      entityType: 'PAGE',
      moduleType: 'COMPANIES',
    })),
};
