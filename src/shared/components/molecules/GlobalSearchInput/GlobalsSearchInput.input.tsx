import { useSearchParams } from 'next/navigation';
import React, {
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import useSearchFilters from '@shared/hooks/searchFilters/useSearchFilters';
import { useGetLocationValueInFilter } from '@shared/hooks/useGetLocationValueInFilter';
import { isBusinessApp } from '@shared/utils/getAppEnv';
import { useDebounceState } from '@shared/utils/hooks/useDebounceState';
import { mutableStore } from 'shared/constants/mutableStore';
import { useGlobalState } from 'shared/contexts/Global/global.provider';
import { useAuthCountry } from 'shared/hooks/useAuthCountry';
import useGlobalSearchUtilities from 'shared/hooks/useGlobalSearchUtilities';
import Flex from 'shared/uikit/Flex';
import SearchInputV2 from 'shared/uikit/SearchInputV2';
import cnj from 'shared/uikit/utils/cnj';
import useMedia from 'shared/uikit/utils/useMedia';
import geoApi from 'shared/utils/api/geo';
import { depotsEndpoints } from 'shared/utils/constants/servicesEndpoints';
import useClickOutside from 'shared/utils/hooks/useClickOutside';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './GlobalsSearchInput.input.module.scss';
import { normalizers } from './utils/normalizers';
import { searchGlobally } from './utils/searchGlobally';
import type { SearchResultItem } from 'shared/components/Organism/types';
import type { ApiType, TextArg } from 'shared/hooks/useGlobalSearchUtilities';

export interface InputProps {
  inputStyle?: string;
  placeholder: string;
  contentClassName?: string;
  searchIconProps?: any;
  inputRef?: React.Ref<any>;
  trashIconClassName?: string;
  emptyMessage?: React.ReactElement;
  children?: (props: any) => React.ReactNode;
  onClose: () => void;
  defaultValue?: string;
  name: 'location' | 'text';
  setFocusedInput: (s: string) => void;
  focusedInput: string;
  countryCode?: string;
  ref?: React.Ref<HTMLImageElement>;
  additionalRightIcon?: React.ReactElement;
}

const Input = ({
  inputStyle,
  placeholder,
  children,
  contentClassName,
  trashIconClassName,
  searchIconProps,
  inputRef,
  emptyMessage,
  onClose,
  name,
  defaultValue = '',
  setFocusedInput,
  focusedInput,
  countryCode,
  ref,
  additionalRightIcon,
}: InputProps) => {
  const { t } = useTranslation();
  const isOpenRightPanel = useGlobalState('isOpenRightPanel');
  const isOpenChatPanel = useGlobalState('isOpenChatPanel');
  const wrapperRef = useRef<HTMLDivElement>(null);
  const { isMoreThanTablet } = useMedia();
  const [show, toggleShow] = useState(false);
  const {
    value,
    setValue: onChangeText,
    debounceValue,
  } = useDebounceState<string>(defaultValue, 500);
  const locationValueInFilter = useGetLocationValueInFilter();

  const isLocation = name === 'location';
  const remoteItem = {
    isSpecific: true,
    type: 'REMOTE',
    id: 'REMOTE',
    label: t('REMOTE'),
    icon: 'house-light',
  };
  const {
    currentModule,
    onSelectHandler: onSelect,
    getQueryKey,
    searchForText,
  } = useGlobalSearchUtilities();
  const { refetchWithCurrentEntityId } = useSearchFilters();
  const searchParams = useSearchParams();
  const searchQuery = decodeURIComponent(searchParams.get('query') || '');
  const isHashtag = value?.startsWith('#');
  const isJobs = currentModule === 'jobs';

  const showingResults = (show || !isMoreThanTablet) && name === focusedInput;
  const authCountry = useAuthCountry();

  function onChange(val: string) {
    onChangeText(val);
  }

  const {
    refetch: refetchSearch,
    data: searchResult = [] as any,
    isLoading,
  } = useReactQuery({
    action: {
      apiFunc: (textArg: TextArg) => searchGlobally(textArg, currentModule),
      key: getQueryKey(currentModule, 'items', debounceValue),
      params: { text: debounceValue },
    },
    config: {
      enabled: false,
    },
  });
  const valueWithDefault = isLocation && !value ? 'a' : value;

  const {
    refetch: refetchFilteredLocations,
    data: filteredLocations = [] as any,
    isFetching: isLoadingLocation,
  } = useReactQuery({
    action: {
      spreadParams: true,
      apiFunc: geoApi.suggestPlace,
      key: [placeholder, valueWithDefault],
      params: { text: valueWithDefault, countryCode },
    },
    config: { enabled: false },
  });

  const {
    refetch: refetchDataSuggestions,
    data: dataSuggestionsGeneral = {},
    isFetching: isLoadingSuggestions,
    isStale,
  } = useReactQuery<any>({
    action: {
      key: getQueryKey('all', 'recents', 'suggestions'),
      url: depotsEndpoints.getHistory,
      params: {},
      beforeCache: (data: any) => ({
        recents: normalizers.recents(
          data?.searchHistory,
          isJobs ? 'jobs' : 'all'
        ),
        recentLocations: normalizers.recents(data?.searchHistory, 'locations'),
        rawSuggestions: data?.suggestionHistory,
      }),
    },
    config: {
      enabled: false,
    },
  });

  useEffect(() => {
    if (!show) return;
    if (isStale) {
      refetchDataSuggestions();
    }
    if (value !== '') {
      refetchSearch();
    }
    if (name === 'location') {
      refetchFilteredLocations();
    }
  }, [debounceValue, name, show, currentModule]);

  const {
    recents = [],
    rawSuggestions = [],
    recentLocations,
  } = dataSuggestionsGeneral;

  const dataSuggestions = normalizers.suggestions(
    rawSuggestions,
    isJobs ? ['JOB'] : undefined
  );

  const locationSuggestions = [
    ...normalizers
      .suggestions({ locations: [...(rawSuggestions?.locations || [])] }, [
        'LOCATION',
      ])
      ?.map((item) => ({ ...item, dontSearchSet: true })),
  ]?.reverse();
  const dataRecentLocations = recentLocations
    ?.slice(0, 2)
    ?.map((item) => ({ ...item, dontSearchSet: true }));

  const dataRecents = recents?.slice(0, 2);
  const isLoadingOverall = isLocation ? isLoadingLocation : isLoading;

  const onFocusHandler = () => {
    toggleShow(true);
    setFocusedInput(name);
  };

  const handleClickOutside = () => {
    setTimeout(() => {
      toggleShow(false);
      onClose?.();
      const input = wrapperRef.current?.querySelector('input');
      input?.blur?.();
    }, 0);
  };

  const closeOutside = () => {
    if (!isMoreThanTablet) return;
    handleClickOutside();
    // if (isBusinessApp) return;
    if (isLocation) {
      simulatePressEnter();
    } else if (searchQuery !== value) {
      searchForText(value);
    }
  };

  const onSelectHandler = (item: SearchResultItem, type: ApiType) => {
    if (item.type === 'REMOTE') {
      onChange(locationValueInFilter.placeTitleDefault);
    }
    if (!item?.isSpecific && item?.type !== 'HASHTAG') {
      setTimeout(() => onChange(item?.label), 0);
    }
    onSelect[type](item);
    handleClickOutside();
  };
  const onKeyDown = (e: any) => {
    const val = e.target.value;
    if (e.key === 'Enter') {
      if (isLocation) {
        const item: any =
          filteredLocations?.length === 1
            ? filteredLocations?.[0]
            : {
                label: authCountry?.title,
                value: authCountry?.countryCode,
                isDefault: true,
              };
        onSelectHandler(
          {
            ...item,
            moduleType: 'LOCATIONS',
            entityType: 'LOCATION',
          },
          'location'
        );
        setTimeout(refetchWithCurrentEntityId, 0);
      } else {
        onChange(val);
        searchForText(val, isBusinessApp ? currentModule : undefined);
        handleClickOutside();
      }
    } else if (!show) toggleShow(true);
  };
  const simulatePressEnter = (val = inputRef.current?.value || value) => {
    onKeyDown({ target: { value: val }, key: 'Enter' });
  };

  const onClearSearch = () => {
    if (!wrapperRef.current) return;
    onChange('');
    const input = wrapperRef.current?.querySelector('input');
    input?.focus?.();
  };

  // useEffect(() => {
  //   // Purge the input when navigating in the website, except for the
  //   // search pages or the search result pages
  //   // if (moduleSpecificSearchResultPage.isActive) return;
  //   if (pathname.includes(routeNames.search)) return;
  //   onChange('');
  // }, [pathname]);

  useEffect(() => {
    if (value === searchQuery || isLocation) return;
    onChange(searchQuery);
  }, [searchQuery]);

  useEffect(() => {
    if (!isMoreThanTablet && !isLocation) {
      const input = wrapperRef.current?.querySelector('input');
      input?.focus?.();
    }
  }, []);

  useImperativeHandle(ref, () => ({ simulatePressEnter }));

  return (
    <Flex className={classes.inputClassName} ref={wrapperRef}>
      <SearchInputV2
        ref={inputRef}
        variant={isBusinessApp ? 'square' : 'circle'}
        placeholder={placeholder}
        value={value}
        inputStyle={cnj(
          classes.inputStyle,
          isHashtag && classes.hashtagInput,
          inputStyle
        )}
        onChange={onChange}
        onClearSearch={onClearSearch}
        onFocus={onFocusHandler}
        inputProps={{
          value,
          autoComplete: 'off',
          maxLength: 150,
          onKeyDown,
        }}
        trashIconProps={{
          color: 'thirdText',
          className: trashIconClassName,
        }}
        searchIconProps={searchIconProps}
        additionalRightIcon={additionalRightIcon}
      />
      <Flex
        key={name}
        className={cnj(
          classes.root,
          showingResults ? classes.show : classes.hide
        )}
      >
        <Flex
          className={cnj(
            classes.content,
            isJobs && classes.topGutter,
            contentClassName
          )}
        >
          {children({
            isLoading: isLoadingOverall,
            emptyMessage,
            onSelectHandler,
            filteredOption: isLocation
              ? filteredLocations?.length
                ? [...filteredLocations, remoteItem]
                : []
              : searchResult,
            inputValue: value || '',
            dataRecents: isLocation ? dataRecentLocations : dataRecents,
            dataSuggestions: isLocation
              ? [...locationSuggestions, remoteItem]
              : dataSuggestions,
            isLocation,
            show,
            handleClickOutside,
          })}
        </Flex>
        {showingResults && (
          <Layover
            {...{ isOpenChatPanel, closeOutside, isOpenRightPanel, wrapperRef }}
          />
        )}
      </Flex>
    </Flex>
  );
};

export default Input;

function Layover({
  closeOutside,
  isOpenRightPanel,
  isOpenChatPanel,
  wrapperRef,
}: any) {
  const onClickOutside = useCallback(
    (e: any) => {
      closeOutside?.();
    },
    [closeOutside]
  );

  useClickOutside(wrapperRef, {
    onClickOutside,
  });

  return (
    <Flex
      className={cnj(
        classes.layover,
        (isOpenRightPanel ||
          isOpenChatPanel ||
          !mutableStore.hasMessagePanelOnRightSide) &&
          classes.layoverRight
      )}
      onClick={onClickOutside}
    />
  );
}
