import pickBy from 'lodash/pickBy';
import React, { useCallback, useMemo } from 'react';
import Avatar from 'shared/uikit/Avatar';
import BaseButton from 'shared/uikit/Button/BaseButton';
import IconButton from 'shared/uikit/Button/IconButton';
import DividerVertical from 'shared/uikit/Divider/DividerVertical';
import Flex from 'shared/uikit/Flex';
import HeroIcon from 'shared/uikit/HeroIcon';
import Typography from 'shared/uikit/Typography';
import cnj from 'shared/uikit/utils/cnj';
import useTranslation from 'shared/utils/hooks/useTranslation';
import preventClickHandler from 'shared/utils/toolkit/preventClickHandler';
import classes from './GlobalSearchInput.resultItem.module.scss';
import type { SearchResultItem } from 'shared/components/Organism/types';
import type { ApiType } from 'shared/hooks/useGlobalSearchUtilities';
import type HeroIconProps from 'shared/uikit/HeroIconProps';

const BoldItems: SearchResultItem['type'][] = [
  'PERSON',
  'PAGE',
  'HASHTAG',
  'BUSINESS_JOB',
  'COMPANIES',
  'CANDIDATE',
];

export interface ResultItemProps {
  className?: string;
  deleteAction?: (...args: any[]) => any;
  onClick?: (...args: any[]) => void;
  renderTitle?: React.ReactNode;
  resent?: boolean;
  item: SearchResultItem;
  itemType: ApiType;
  isLocation?: boolean;
  boldDisabled?: boolean;
}

const defaultHeroIconProps = {
  size: 32,
  iconSize: 16,
  iconType: 'far',
  iconName: 'search',
  color: 'themedGray',
};

const ResultItem = ({
  className,
  deleteAction,
  onClick,
  renderTitle,
  item,
  itemType,
  isLocation,
  boldDisabled,
}: ResultItemProps): JSX.Element => {
  const { t } = useTranslation();
  const {
    label: title,
    subtitle,
    imgUrl: imgSrc,
    type,
    isSpecific,
    secondaryTitle,
  } = item;

  const clickHandler = useCallback(() => onClick(item), [onClick, item]);
  const isJobSpecific = type === 'JOB' && isSpecific;

  const heroIconProps: Record<ApiType, Partial<HeroIconProps>> = useMemo(
    () => ({
      items: {
        iconName:
          type === 'PROJECT'
            ? 'projects'
            : isJobSpecific
              ? 'briefcase-blank-light'
              : type === 'HASHTAG'
                ? 'hashtag'
                : type === 'LOCATION' || isLocation
                  ? item.icon || 'map-marker-alt'
                  : undefined,
        color:
          type === 'HASHTAG'
            ? 'brand'
            : isJobSpecific
              ? 'disabledGray_graphene'
              : 'smoke_coal',
        iconSize:
          type === 'PROJECT'
            ? 20
            : type === 'HASHTAG'
              ? 14
              : type === 'JOB' && !isJobSpecific
                ? undefined
                : type === 'LOCATION' || isLocation
                  ? 16
                  : 24,
        variant: isJobSpecific ? 'square' : 'circle',
        iconType: type === 'PROJECT' ? 'fas' : 'far',
      },
      recents: {
        iconSize: 18,
        iconType: 'fas',
        iconName: 'history',
      },
      suggestions: {},
      all: {
        color: 'brandWhite',
      },
      location: {},
      locationRecents: {},
      locationSuggestions: {
        iconName: item.icon || 'map-marker-alt',
        iconSize: 16,
      },
    }),
    [type]
  );
  const props = {
    ...defaultHeroIconProps,
    ...pickBy(heroIconProps[itemType], (v) => v !== undefined),
  };

  const titleBoldProps = {
    size: 16,
    font: 700,
    color: 'smoke_coal',
  };
  const isBold = useMemo(
    () => BoldItems.includes(type) && !boldDisabled,
    [type, boldDisabled]
  );

  const isAvatar =
    itemType === 'items' &&
    ['PAGE', 'PERSON', 'CANDIDATE', 'BUSINESS_JOB', 'COMPANIES'].includes(type);

  return (
    <BaseButton
      onClick={clickHandler}
      className={cnj(classes.resultItemRoot, className)}
    >
      <Flex className={cnj(classes.avatarWrapper)}>
        {isAvatar ? (
          <Avatar
            size="xxs"
            resolution="tiny"
            imgSrc={imgSrc}
            name={title}
            isCompany={
              type === 'COMPANIES' || type === 'PAGE' || type === 'BUSINESS_JOB'
            }
          />
        ) : (
          <HeroIcon {...(props as HeroIconProps)} />
        )}
      </Flex>
      <Flex className={classes.titleWrapper}>
        <Flex flexDir="row">
          {renderTitle || (
            <Typography
              lineHeight={21}
              isWordWrap
              isTruncated
              lineNumber={1}
              size={15}
              font="400"
              color="smoke_coal"
              {...(isBold && titleBoldProps)}
            >
              {title}
            </Typography>
          )}
          {!!secondaryTitle && (
            <DividerVertical className={classes.verticalDivider} />
          )}
          {secondaryTitle && (
            <Typography size={12} color="disabledGrayDark_gray">
              {t(secondaryTitle || '')}
            </Typography>
          )}
        </Flex>
        {subtitle && (
          <Typography
            font="400"
            size={12}
            height={14}
            color="secondaryDisabledText"
            isWordWrap
            isTruncated
            lineNumber={1}
          >
            {subtitle}
          </Typography>
        )}
      </Flex>
      {deleteAction && (
        <IconButton
          onClick={(e) => {
            preventClickHandler(e);
            deleteAction?.();
          }}
          name="times"
          size="md18"
          colorSchema="transparent"
          className={classes.deleteAction}
        />
      )}
    </BaseButton>
  );
};

export default ResultItem;
