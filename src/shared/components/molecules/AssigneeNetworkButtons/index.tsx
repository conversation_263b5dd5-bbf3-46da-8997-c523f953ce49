import { useRouter } from 'next/navigation';
import * as React from 'react';
import { roomMemberRoles } from '@shared/components/Organism/Message/constants';
import useGetAppObject from '@shared/hooks/useGetAppObject';
import useOpenMessage from '@shared/hooks/useOpenMessage';
import { useSchedulesCalendar } from '@shared/hooks/useSchedulesCalendar';
import { MeetingDatetimeType } from '@shared/types/schedules/schedules';
import Button from '@shared/uikit/Button';
import IconButton from '@shared/uikit/Button/IconButton';
import Flex from '@shared/uikit/Flex';
import { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { dayjs } from '@shared/utils/Time';
import Link from 'shared/uikit/Link';
import type { UserApiResponse } from '@shared/types/user';

interface IAssigneeNetworkButtonsProps {
  user: UserApiResponse;
  hasCreateMeeting?: boolean;
}

const AssigneeNetworkButtons: React.FunctionComponent<
  IAssigneeNetworkButtonsProps
> = ({ user, hasCreateMeeting = false }) => {
  const { t } = useTranslation();
  const { isAuthUserBusinessOwner: isOwner, authUser } = useGetAppObject();
  const router = useRouter();
  const { openCreateEventWithDate } = useSchedulesCalendar();

  const openMessage = useOpenMessage();
  const openMessageHandler = () => {
    openMessage({
      isGroupChat: false,
      id: user.id,
      icon: user.croppedImageUrl,
      name: `${user.name} ${user.surname}`,
      owner: user.id,
      username: user.username,
      createdAt: new Date(),
      role: roomMemberRoles.Owner,
      isPage: false,
    });
  };

  const habdleViewProfile = () => router.push(`/${user?.username}`);

  const handleCreateEvent = React.useCallback(() => {
    const assigneeUser =
      authUser?.id === user.id
        ? {}
        : {
            ...user,
            id: user.id,
          };
    openCreateEventWithDate(
      dayjs().add(1, 'day'),
      {
        schedulesEventType: ScheduleEventTypes.MEETING,
        targetAttendee: assigneeUser,
        datetimeType: MeetingDatetimeType.PROVIDE_AVAILABILITY,
      },
      true
    );
  }, [user]);

  if (isOwner)
    return (
      <Link to={`/${user?.username}`} className="w-full">
        <Button
          fullWidth
          label={t('view_profile')}
          schema="semi-transparent2"
          leftIcon="user-collaborator"
          leftType="far"
          leftSize={16}
        />
      </Link>
    );

  if (hasCreateMeeting) {
    return (
      <Flex className="!flex-row gap-8 flex-1">
        <Button
          label={t('message')}
          fullWidth
          schema="semi-transparent2"
          leftIcon="envelope"
          leftType="far"
          leftSize={16}
          onClick={openMessageHandler}
          className="flex-1"
        />
        <IconButton
          type="fal"
          size="md15"
          variant="rectangle"
          name="calendar"
          colorSchema="graySecondary"
          onClick={handleCreateEvent}
        />
      </Flex>
    );
  }

  return (
    <Flex className="!flex-row gap-8 flex-1">
      <Button
        label={t('message')}
        fullWidth
        schema="semi-transparent2"
        leftIcon="envelope"
        leftType="far"
        leftSize={16}
        onClick={openMessageHandler}
      />
      <Button
        fullWidth
        label={t('view_profile')}
        leftIcon="user-collaborator"
        leftType="far"
        leftSize={16}
        onClick={habdleViewProfile}
      />
    </Flex>
  );
};

export default AssigneeNetworkButtons;
