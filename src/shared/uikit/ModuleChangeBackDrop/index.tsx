'use client';

import React from 'react';
import <PERSON><PERSON> from 'react-lottie';
import useModuleSwitchLoading from 'shared/hooks/useModuleSwitchLoading';
import useTranslation from 'shared/utils/hooks/useTranslation';
import translateReplacer from 'shared/utils/toolkit/translateReplacer';
import loadingAnimation from '../../../../public/assets/lotties/loading.json';
import Flex from '../Flex';
import Typography from '../Typography';
import classes from './index.module.scss';

const ModuleChangeBackDrop: React.FC = () => {
  const { t } = useTranslation();
  const { isLoading, distinctionPortal } = useModuleSwitchLoading();

  if (!isLoading) return <></>;

  return (
    <Flex className={classes.container}>
      <Flex className={classes.centeredBox}>
        <Flex>
          <Lottie
            isClickToPauseDisabled
            options={{
              loop: true,
              autoplay: true,
              animationData: loadingAnimation,
            }}
            width={100}
            height={100}
          />
        </Flex>
        <Typography color="white" size={15} font="500" mt={12}>
          {translateReplacer(t('switching_to_lobox_portal'), [
            distinctionPortal?.toUpperCase() || '',
          ])}
        </Typography>
      </Flex>
    </Flex>
  );
};

export default ModuleChangeBackDrop;
