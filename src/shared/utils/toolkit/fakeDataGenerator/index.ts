import { generateFakeCandidates } from './candidateGenerator';
import { generateFakeCompanies } from './companyGenerator';
import type { AnyRecord } from './shared';

export function generateFakeData(
  base: AnyRecord,
  seedKey: string,
  count: number = 5
): AnyRecord[] {
  const featureName = base.featureName as string;

  switch (featureName) {
    case 'CANDIDATE_SEARCH_LIMITATION':
      return generateFakeCandidates(base, seedKey, count);

    case 'SEARCH_COMPANIES':
      return generateFakeCompanies(base, seedKey, count);

    // Add more cases for other feature types as needed
    // case 'JOB_SEARCH_LIMITATION':
    //   return generateFakeJobs(base, seedKey, count);

    default:
      // Fallback to candidates for unknown features
      return generateFakeCandidates(base, seedKey, count);
  }
}

// Export individual generators for direct use
export { generateFakeCandidates } from './candidateGenerator';
export { generateFakeCompanies } from './companyGenerator';
export { handlePlanLimitedResponse } from './responseHandler';
export type { AnyRecord } from './shared';
