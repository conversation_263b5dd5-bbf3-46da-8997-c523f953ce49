import {
  hashCode,
  mulberry32,
  pick,
  FAKE_CITIES,
  type AnyRecord,
} from './shared';

const FAKE_COMPANY_NAMES = [
  'TechCorp Solutions',
  'Digital Innovations Ltd',
  'Future Systems Inc',
  'Global Tech Partners',
  'Smart Solutions GmbH',
  'NextGen Technologies',
  'Advanced Software Co',
  'Modern Tech Group',
  'Elite Digital Services',
  'Premier Tech Solutions',
];

const FAKE_COMPANY_INDUSTRIES = [
  'Technology',
  'Software Development',
  'Digital Marketing',
  'E-commerce',
  'Fintech',
  'Healthcare Tech',
  'EdTech',
  'AI & Machine Learning',
  'Cybersecurity',
  'Cloud Services',
];

function generateLockedFakeCompany(
  index: number,
  seedKey: string,
  base: AnyRecord
): AnyRecord {
  const rng = mulberry32(hashCode(`${seedKey}:company:${index}`));
  const companyName = pick(FAKE_COMPANY_NAMES, rng);
  const city = pick(FAKE_CITIES, rng);
  const id = `fake_company_${seedKey}_${index}`;

  return {
    id,
    pageId: base?.currentEntity?.id ?? null,
    userType: null,
    username: `locked-company-${id}`,
    title: companyName,
    category: 'COMPANY',
    croppedImageData: 'fake_image_data',
    croppedImageUrl: `https://storage.googleapis.com/lobox_public_images/image/original/fake_${id}.jpeg`,
    locations: [
      {
        title: city.title,
        primaryLocation: true,
      },
    ],
    jobCount: Math.floor(rng() * 10),
    collaboratorCount: Math.floor(rng() * 10),
    candidateCount: Math.floor(rng() * 10),
    companyRole: 'MEMBER',
    vendorClientStatus: 'ACTIVE',
    isFake: true,
  };
}

export function generateFakeCompanies(
  base: AnyRecord,
  seedKey: string,
  count: number = 5
): AnyRecord[] {
  return Array.from({ length: count }, (_v, i) =>
    generateLockedFakeCompany(i, seedKey, base)
  );
}
