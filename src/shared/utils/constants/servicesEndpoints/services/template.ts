import type { TemplateCategoryType } from '@shared/utils/api/template';

const TEMPLATE_SERVICE = 'template-service';
const API_VERSION = '/api/v1';

const templateEndPoints = {
  searchAll: (category: TemplateCategoryType) =>
    `${TEMPLATE_SERVICE}${API_VERSION}/template/${category}/search`,
  getAll: `${TEMPLATE_SERVICE}${API_VERSION}/template/email`,
  getAllMeetings: `${TEMPLATE_SERVICE}${API_VERSION}/template/meeting/search`,
  getMeeting: (id: string) =>
    `${TEMPLATE_SERVICE}${API_VERSION}/template/meeting/${id}`,
  setDefaultMeetingTemplate: (id: string) =>
    `${TEMPLATE_SERVICE}${API_VERSION}/template/meeting/default/${id}`,

  byId: (category: TemplateCategoryType, id?: string) =>
    `${TEMPLATE_SERVICE}${API_VERSION}/template/${category}${id ? `/${id}` : ''}`,
  emailSearch: `${TEMPLATE_SERVICE}${API_VERSION}/template/email/search`,
  createEmail: `${TEMPLATE_SERVICE}${API_VERSION}/template/email`,
  getEmailTemplate: (id: number) =>
    `${TEMPLATE_SERVICE}${API_VERSION}/system/template/email/${id}`,
  updateEmailTemplate: (id: number) =>
    `${TEMPLATE_SERVICE}${API_VERSION}/template/email/title/${id}`,
  setEmailTemplateDefault: (id: number) =>
    `${TEMPLATE_SERVICE}${API_VERSION}/template/email/default/${id}`,
  removeEmailTemplateDefault: (id: number) =>
    `${TEMPLATE_SERVICE}${API_VERSION}/template/email/default/${id}`,
  removeEmailTemplate: (id: number) =>
    `${TEMPLATE_SERVICE}${API_VERSION}/template/email/${id}`,

  toggleDefault: (category: TemplateCategoryType, id: string) =>
    `${TEMPLATE_SERVICE}${API_VERSION}/template/${category}/default/${id}`,
};

export default templateEndPoints;
