import last from 'lodash/last';
import type { TodoStatusType } from '@shared/types/todo';

const CANDIDATE_SERVICE = 'candidate-service';

const API_VERSION = '/api/v1';

const base = `${CANDIDATE_SERVICE}${API_VERSION}`;

const candidate = {
  filter: `${base}/candidate/filter`,
  search: `${base}/candidate/search`,
  createCandidate: `${base}/candidate`,
  listCandidates: `${base}/candidate`,
  createCandidateMeeting: (candidateId: string) =>
    `${base}/candidate/meeting/${candidateId}`,
  editBasicInfoById: (id: string) =>
    `${base}/candidate/basic-information/${id}`,
  editSocialInfoById: (id: string) =>
    `${base}/candidate/social-information/${id}`,
  editPreferenceInfoById: (id: string) =>
    `${base}/candidate/expected-information/${id}`,
  editLegalInfoById: (id: string) =>
    `${base}/candidate/legal-information/${id}`,
  editDemographicInfoById: (id: string) =>
    `${base}/candidate/background-information/${id}`,
  editAdditionalInfoById: (id: string) =>
    `${base}/candidate/additional-information/${id}`,
  editResumeById: (id: string) => `${base}/candidate/resume/${id}`,
  singleCandidateById: (id: string) => `${base}/candidate/${id}`,
  singleExperienceById: (id: string) => `${base}/candidate/experience/${id}`,
  singleEducationById: (id: string) => `${base}/candidate/education/${id}`,
  singleSkillById: (id: string) => `${base}/candidate/skill/${id}`,
  singleLanguageById: (id: string) => `${base}/candidate/language/${id}`,
  similarCandidatesById: (id: string) => `${base}/candidate/similar/${id}`,
  setLockMode: (id: string) => `${base}/candidate/lock/${id}`,

  todos: `${base}/candidate/todo`,
  filterTodos: `${base}/todo/filter`,
  singleTodoById: (id: string) => `${base}/candidate/todo/${id}`,
  setTodoStatus: ({ id, status }: { id: string; status: TodoStatusType }) =>
    `${base}/candidate/todo/${last(status.toLowerCase().split('_'))}/${id}`,
  addTodo: (candidateId: number) => `${base}/candidate/todo/${candidateId}`,
  editTodo: (todoId: string) => `${base}/candidate/todo/${todoId}`,
  deleteTodo: (todoId: string) => `${base}/candidate/todo/${todoId}`,
  batchAddTodo: `${base}/candidate/todo/batch`,

  notes: `${base}/candidate/note`,
  singleNoteById: (id: string) => `${base}/candidate/note/${id}`,
  searchNote: `${base}/note/search`,
  batchAddNote: `${base}/candidate/note/batch`,
  addNoteCandidate: (candidateId: number) =>
    `${base}/candidate/note/${candidateId}`,
  editNoteCandidate: (noteId: string) => `${base}/candidate/note/${noteId}`,
  deleteNoteCandidate: (noteId: string) => `${base}/candidate/note/${noteId}`,

  reviews: (candidateId: string) => `${base}/review/${candidateId}`,
  singleReviewById: (id: string) => `${base}/review/${id}`,
  addCandidateReview: (candidateId: string) =>
    `${base}/candidate/review/${candidateId}`,
  editCandidateReview: (reviewId: string) =>
    `${base}/candidate/review/${reviewId}`,
  removeCandidateReview: (reviewId: string) =>
    `${base}/candidate/review/${reviewId}`,
  getCandidateReviews: (candidateId: string) =>
    `${base}/candidate/review/${candidateId}`,

  meetings: `${base}/candidate/meeting`,
  singleMeetingById: (id: string) => `${base}/candidate/meeting/${id}`,
  upcomingMeetings: `${base}/candidate/meeting/upcoming`,
  pastMeetings: `${base}/candidate/meeting/past`,
  suggestCandidates: `${base}/candidate/suggest`,

  getSummary: (id: string) => `${base}/candidate/summary/${id}`,
  candidateLastView: (id: string) => `${base}/candidate/last-view/${id}`,
  addSavedFilter: `${base}/candidate/filter/saved`,
  savedCandidate: (id: string) => `${base}/candidate/saved/${id}`,
  editSavedFilterById: (id: string) => `${base}/candidate/filter/saved/${id}`,
  removeSavedFilterById: (id: string) => `${base}/candidate/filter/saved/${id}`,
  getAllSavedFilters: `${base}/candidate/filter/saved`,
  uploadResume: `${base}/resume/upload`,
  candidateCompare: (id: string) =>
    `${base}/candidate/compare?candidateIds=${id}`,
  searchCandidateItem: `${base}/candidate/search/item`,
  similarCandidateItem: (id: string) => `${base}/candidate/similar/item/${id}`,
  updateBulkEmail: `${base}/candidate/email/batch`,
  updateBulkTodo: `${base}/candidate/todo/batch`,
  updateBulkNote: `${base}/candidate/note/batch`,
};

export default candidate;
