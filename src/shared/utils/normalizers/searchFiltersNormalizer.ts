import { APP_ENTITIES } from '@shared/utils/constants';
import isEmpty from '@shared/utils/toolkit/isEmpty';
import { addPrefix } from '../toolkit/addPrefix';

type AnyObj = Record<string, any>;

const nameCodeToLabelValue = (city: any) => ({
  label: city?.name,
  value: city?.code,
});

const titleIdToLabelValue = (item: any) => ({
  label: item?.title,
  value: item?.id,
});

const nameIdToLabelValue = (item: any) => ({
  label: item?.name,
  value: item?.id,
});

const stringToLabelValue = (x: string) => ({
  label: x,
  value: x,
});

const stringToLowerCaseLabelValue = (x: string) => ({
  label: x?.toLowerCase(),
  value: x,
});

const stringToLabelValueWithPrefix = (x: string) => ({
  label: addPrefix(x),
  value: x,
});

const titleLanguageIdToLabelValue = (l: any) => ({
  label: l?.title,
  value: l?.languageId,
});

const titleIdToLabelValueWithMeta = (c: any) => ({
  label: c?.title,
  value: c?.id,
  isPage: c?.type === APP_ENTITIES.page,
  helperText: `@${c?.username}`,
});

const titleIdToLabelValueWithImage = (i: any) => ({
  label: i?.title,
  value: i?.id,
  image: i?.imageUrl,
});

const hashtagToLabelValue = (h: string) => ({
  label: `#${h}`,
  value: h,
});

const companySizeToLabelValue = (s: any) => ({
  label: `COMPANY_SIZE_${s}`,
  value: s,
});

const pageTypeToLabelValue = (c: string) => ({
  label: `COMPANY_TYPE_${c}`,
  value: c,
});

const nameIdToLabelValueWithMeta = (u: any) => ({
  label: u?.name
    ? `${u.name}${u.surname ? ` ${u.surname}` : ''}`
    : (u?.username ?? u?.email),
  value: u?.id,
  image: u?.croppedImageUrl ?? u?.imageUrl,
  helperText: u?.username ? `@${u.username}` : undefined,
});

const nameOrTitleIdToLabelValueWithImage = (p: any) => ({
  label: p?.name ?? p?.title,
  value: p?.id,
  image: p?.croppedImageUrl ?? p?.imageUrl,
  helperText: p?.username ? `@${p.username}` : undefined,
});

const skillTitleOrNameToLabelValue = (s: any) => ({
  label: s?.skillName ?? s?.title,
  value: s?.skillId ?? s?.skillName,
});

const workAuthorizationTitleIdToLabelValue = (w: any) => ({
  label: w?.title
    ? `${w.title}${w?.countryCode ? ` (${w.countryCode})` : ''}`
    : undefined,
  value: w?.id,
});

const userToLabelValue = (u: AnyObj) => ({
  label:
    u?.fullName ??
    (u?.name
      ? `${u.name}${u.surname ? ` ${u.surname}` : ''}`
      : (u?.username ?? u?.email)),
  value: u?.id ?? u?.userId,
  image: u?.croppedImageUrl ?? u?.imageUrl,
  helperText: u?.usernameAtSign ?? (u?.username ? `@${u.username}` : undefined),
});

const pageToLabelValue = (p: AnyObj) => ({
  label: p?.name ?? p?.title,
  value: p?.id,
  image: p?.croppedImageUrl ?? p?.imageUrl,
  helperText: p?.username ? `@${p.username}` : undefined,
});

const languageToLabelValue = (l: AnyObj) => ({
  label: l?.languageName ?? l?.title ?? l?.name,
  value: l?.languageId ?? l?.id ?? l?.title ?? l?.name,
});

const cityToLabelValue = (c: AnyObj) => ({
  label: c?.name ?? c?.label,
  value: c?.code ?? c?.cityCode ?? c?.value,
});

const skillToLabelValue = (s: AnyObj) => ({
  label: s?.skillName ?? s?.title,
  value: s?.skillId ?? s?.skillName ?? s?.title,
});

export const userSideSearchFilters = (data: any): any => ({
  pages:
    data?.companies?.map((page: any) => ({
      value: page.id,
      label: page.title,
      image: page.croppedImageUrl,
      helperText: `@${page?.username}`,
    })) ?? [],

  relatedPages: data?.pages?.map(nameIdToLabelValueWithMeta) ?? [],
  industries: data?.industries?.map(titleIdToLabelValueWithImage) ?? [],
  languages:
    data?.languages
      ?.map(titleLanguageIdToLabelValue)
      ?.filter((i: any) => i.value && i.label) ?? [],
  cities: data?.cities?.map(nameCodeToLabelValue) ?? [],
  schools: data?.schools?.map(titleIdToLabelValue) ?? [],
  titles: data?.occupationNames?.map(stringToLabelValue) ?? [],
  educationDegrees: data?.educationDegrees?.map(stringToLabelValue) ?? [],
  creators: data?.owners?.map(titleIdToLabelValueWithMeta) ?? [],
  companySizes: data?.companySizes?.sort()?.map(companySizeToLabelValue) ?? [],
  pageTypes: data?.categories?.map(pageTypeToLabelValue) ?? [],
  hashtags: data?.hashtags?.map(hashtagToLabelValue) ?? [],
  postedBy: data?.postedBy?.map(stringToLabelValueWithPrefix) ?? [],
  postTypes: data?.types?.map(stringToLabelValueWithPrefix) ?? [],
  datePosted: data?.datePosted?.map(stringToLabelValueWithPrefix) ?? [],
  sortBy: data?.sortBy?.map(stringToLabelValueWithPrefix) ?? [],
  memberSince: data?.memberSince?.map(stringToLabelValueWithPrefix) ?? [],
  establishmentDate: data?.establishmentDate?.map(stringToLabelValue) ?? [],
  mentions: data?.mentions?.map(titleIdToLabelValueWithMeta) ?? [],
});

export const recruiterJobsFilters = (data: AnyObj): AnyObj => ({
  numberOfFilter: data?.numberOfFilter,
  limitedInPlan: data?.limitedInPlan,
  sortBy: data?.sortBy?.map(stringToLabelValueWithPrefix) ?? [],
  datePosted: data?.datePosted?.map(stringToLabelValue) ?? [],
  statuses: data?.statuses?.map(stringToLabelValue) ?? [],
  priorities: data?.priorities?.map(stringToLabelValue) ?? [],
  responseTimes: data?.responseTimes?.map(stringToLabelValue) ?? [],
  workPlaceTypes: data?.workPlaceTypes?.map(stringToLabelValue) ?? [],
  experienceLevels: data?.experienceLevels?.map(stringToLabelValue) ?? [],
  employmentTypes: data?.employmentTypes?.map(stringToLabelValue) ?? [],
  educationDegrees: data?.educationDegrees?.map(stringToLabelValue) ?? [],
  travelRequirements:
    data?.travelRequirements?.map(stringToLowerCaseLabelValue) ?? [],
  titles: data?.titles?.map(stringToLabelValue) ?? [],
  categories: data?.categories?.map(titleIdToLabelValueWithImage) ?? [],
  creators: data?.creators?.map(nameIdToLabelValueWithMeta) ?? [],
  collaborators: data?.collaborators?.map(nameIdToLabelValueWithMeta) ?? [],
  pointOfContacts: data?.pointOfContacts?.map(nameIdToLabelValueWithMeta) ?? [],
  pages: data?.pages?.map(nameOrTitleIdToLabelValueWithImage) ?? [],
  projects: data?.projects?.map(titleIdToLabelValue) ?? [],
  skills: data?.skills?.map(skillTitleOrNameToLabelValue) ?? [],
  languages:
    data?.languages
      ?.map(titleLanguageIdToLabelValue)
      ?.filter((i: any) => i?.value && i?.label) ?? [],
  cities: data?.cities?.map(nameCodeToLabelValue) ?? [],
  workAuthorization:
    data?.workAuthorization
      ?.map(workAuthorizationTitleIdToLabelValue)
      ?.filter((i: any) => i?.value && i?.label) ?? [],
  benefits:
    data?.benefits
      ?.map((x: AnyObj) => ({
        label: x?.title ?? x?.name ?? x?.label,
        value: x?.id,
      }))
      ?.filter((i: any) => i?.value && i?.label) ?? [],
  hashtags: data?.hashtags?.map(hashtagToLabelValue) ?? [],
  tags: data?.tags?.map(stringToLabelValue) ?? [],
  clientIds: data?.clientIds?.map(stringToLabelValue) ?? [],
  vendorIds: data?.vendorIds?.map(titleIdToLabelValue) ?? [],
  salaryRanges: Array.isArray(data?.salaryRange?.salaryRanges)
    ? data.salaryRange.salaryRanges
        .map((r: AnyObj) => ({
          value: r?.period,
          label: stringToLabelValue(r?.period),
          min: r?.min,
          max: r?.max,
          currency: r?.currencyModel?.code,
          symbol: r?.currencyModel?.symbol,
        }))
        .filter((i: AnyObj) => i.value && i.label)
    : [],
});

export const candidateFilters = (data: AnyObj): AnyObj => ({
  sortBy: data?.sortBy?.map(stringToLabelValueWithPrefix) ?? [],
  memberSince: data?.memberSince?.map(stringToLabelValue) ?? [],
  occupations: data?.occupations?.map(stringToLabelValue) ?? [],
  skills: data?.skills?.map(skillToLabelValue) ?? [],
  languages: data?.languages?.map(languageToLabelValue) ?? [],
  cities: data?.cities?.map(cityToLabelValue) ?? [],
  sources: data?.sources?.map(stringToLabelValue) ?? [],
  clients: data?.clients?.map(stringToLabelValue) ?? [],
  vendors: data?.vendors?.map(titleIdToLabelValue) ?? [],
  salaryRanges: Array.isArray(data?.salaryRange?.salaryRanges)
    ? data.salaryRange.salaryRanges
        .map((r: AnyObj) => ({
          value: r?.period,
          label: stringToLabelValue(r?.period),
          min: r?.min,
          max: r?.max,
          currency: r?.currencyModel?.code,
          symbol: r?.currencyModel?.symbol,
        }))
        .filter((i: AnyObj) => i.value && i.label)
    : [],
  referralUsers: data?.referralUsers?.map(userToLabelValue) ?? [],
  referralCompanies:
    data?.referralCompanies?.map(pageToLabelValue) ??
    data?.referralCompanies?.map(titleIdToLabelValue) ??
    [],
  createBy: data?.createBy?.map(userToLabelValue) ?? [],
  schools: data?.schools?.map(titleIdToLabelValue) ?? [],
  degrees: data?.degrees?.map(stringToLabelValue) ?? [],
  majors: data?.majors?.map(stringToLabelValue) ?? [],
  preferredLocations: data?.preferredLocations?.map(cityToLabelValue) ?? [],
  relocationStatuses: data?.relocationStatuses?.map(stringToLabelValue) ?? [],
  travelRequirements:
    data?.travelRequirements?.map(stringToLowerCaseLabelValue) ?? [],
  noticePeriods: data?.noticePeriods?.map(stringToLabelValue) ?? [],
  genders: data?.genders?.map(stringToLabelValue) ?? [],
  experienceLevels: data?.experienceLevels?.map(stringToLabelValue) ?? [],
  employmentTypes: data?.employmentTypes?.map(stringToLabelValue) ?? [],
  workPlaceTypes: data?.workPlaceTypes?.map(stringToLabelValue) ?? [],
  ageRanges: data?.ageRanges?.map(stringToLabelValueWithPrefix) ?? [],
  races: data?.races?.map(stringToLabelValueWithPrefix) ?? [],
  currentCompanies: data?.currentCompanies?.map(titleIdToLabelValue) ?? [],
  previousCompanies: data?.previousCompanies?.map(titleIdToLabelValue) ?? [],
  veteranStatuses:
    data?.veteranStatuses?.map(stringToLabelValueWithPrefix) ?? [],
  disabilityStatuses:
    data?.disabilityStatuses?.map(stringToLowerCaseLabelValue) ?? [],
  lastActivity: data?.lastActivity?.map(stringToLabelValue) ?? [],
  tags: data?.tags.filter((i) => !isEmpty(i))?.map(stringToLabelValue) ?? [],
  projects: data?.projects?.map(titleIdToLabelValue) ?? [],
  jobs: data?.jobs?.map(titleIdToLabelValue) ?? [],
});

export const jobsFilters = (data: AnyObj): AnyObj => ({
  ...data,
  cities: data?.cities?.map(cityToLabelValue) ?? [],
  titles: data?.titles?.map(stringToLabelValue) ?? [],
  pages: data?.pages?.map(pageToLabelValue) ?? [],
  creators: data?.creators?.map(userToLabelValue) ?? [],
  categories: data?.categories?.map(titleIdToLabelValueWithImage) ?? [],
  skills: data?.skills?.map(skillToLabelValue) ?? [],
  languages:
    data?.languages
      ?.map(languageToLabelValue)
      ?.filter((i: AnyObj) => i.value && i.label) ?? [],
  benefits: data?.benefits?.map(titleIdToLabelValue) ?? [],
  employmentTypes: data?.employmentTypes?.map(stringToLabelValue) ?? [],
  experienceLevels: data?.experienceLevels?.map(stringToLabelValue) ?? [],
  workPlaceTypes: data?.workPlaceTypes?.map(stringToLabelValue) ?? [],
  datePosted: data?.datePosted?.map(stringToLabelValue) ?? [],
  hashtags: data?.hashtags?.map(hashtagToLabelValue) ?? [],
  sortBy: data?.sortBy?.map(stringToLabelValueWithPrefix) ?? [],
});

export const projectsFilters = (data: AnyObj): AnyObj => ({
  creators: data?.creators?.map(nameIdToLabelValueWithMeta) ?? [],
  owners: data?.owners?.map(nameIdToLabelValueWithMeta) ?? [],
  collaborators: data?.collaborators?.map(nameIdToLabelValueWithMeta) ?? [],
  jobs: data?.jobs?.map(titleIdToLabelValue) ?? [],
  tags: data?.tags.filter((i) => !isEmpty(i))?.map(stringToLabelValue) ?? [],
});

export const companyFilters = (data: AnyObj): AnyObj => ({
  categories: data?.categories?.map(stringToLabelValue) ?? [],
  cities: data?.cities?.map(nameCodeToLabelValue) ?? [],
  companySizes: data?.companySizes?.sort()?.map(companySizeToLabelValue) ?? [],
  establishmentDate: data?.establishmentDate?.map(stringToLabelValue) ?? [],
  industries: data?.industries?.map(titleIdToLabelValueWithImage) ?? [],
  sortBy: data?.sortBy?.map(stringToLabelValueWithPrefix) ?? [],
  languages:
    data?.languages
      ?.map(titleLanguageIdToLabelValue)
      ?.filter((i: any) => i?.value && i?.label) ?? [],
});

const searchFiltersNormalizer = {
  userSideSearchFilters,
  recruiterJobsFilters,
  candidateFilters,
  jobsFilters,
  projectsFilters,
  companyFilters,
};

export default searchFiltersNormalizer;
