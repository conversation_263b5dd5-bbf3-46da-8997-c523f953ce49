export type MeetingTemplate = {
  id: number;
  createdDate: string;
  userId: number;
  pageId: number;
  title: string;
  subject: string;
  message: string;
  timeDelay: string;
  fileIds: number[];
  hasFollowup: boolean;
  followupTitle: string;
  followupMessage: string;
  followupPeriod: string;
  followupFileIds: number[];
  default: boolean;
  general: boolean;
};

export type NormalizedMeetingTemplate = {
  title: string;
  subject: string;
  message: string;
  default?: boolean;
};

export const getMeetingTemplateNormalizer = (
  data: MeetingTemplate
): NormalizedMeetingTemplate => {
  const { title, subject, message, default: defaultTemplate } = data;

  return {
    title,
    subject,
    message,
    default: defaultTemplate,
  };
};
