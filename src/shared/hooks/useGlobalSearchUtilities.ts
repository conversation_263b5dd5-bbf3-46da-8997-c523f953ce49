import { useQueryClient } from '@tanstack/react-query';
import useSearchFilters from '@shared/hooks/searchFilters/useSearchFilters';
import { useGetLocationValueInFilter } from '@shared/hooks/useGetLocationValueInFilter';
import { isBusinessApp } from '@shared/utils/getAppEnv';
import isEmpty from '@shared/utils/toolkit/isEmpty';
import { mutableStore } from 'shared/constants/mutableStore';
import {
  searchFilterQueryParams,
  searchGroupTypes,
} from 'shared/constants/search';
import urls from 'shared/constants/urls';
import useNavigateSearchPage from 'shared/hooks/useNavigateSearchPage';
import { routeNames } from 'shared/utils/constants/routeNames';
import useHistory from 'shared/utils/hooks/useHistory';
import useLocation from 'shared/utils/hooks/useLocation';
import useTranslation from 'shared/utils/hooks/useTranslation';
import isRouterAccessibleByPortal from 'shared/utils/isRouterAccessibleByPortal';
import encoder from 'shared/utils/toolkit/encoder';
import { useObjectClicks } from './useObjectClicks';
import useSearchHistory from './useSearchHistory';
import type { SearchResultItem } from 'shared/components/Organism/types';

const modules = isBusinessApp
  ? (['projects', 'candidates', 'recruiterJobs', 'companies'] as const)
  : ([
      'posts',
      'people',
      'pages',
      'jobs',
      'hashtags',
      'locations',
      'all',
    ] as const);

const moduleBaseOnPathname = {
  // [routeNames.jobs]: 'jobs',
  [routeNames.searchJobs]: 'jobs',
  [routeNames.pages]: isBusinessApp ? 'companies' : 'pages',
  [routeNames.searchPages]: isBusinessApp ? 'companies' : 'pages',
  [routeNames.companies.search]: 'companies',
  [routeNames.searchPeople]: 'people',
  [routeNames.searchCandidates]: 'candidates',
  [routeNames.searchRecruiterJobs]: 'recruiterJobs',
  [routeNames.searchRecruiterProjects]: 'projects',
  [routeNames.searchPosts]: 'posts',
  [routeNames.searchHashtags]: 'hashtags',
};

const defaultModule = isBusinessApp ? 'candidates' : 'all';

export type SearchModuleType = (typeof modules)[number];

type LocationItem = {
  category: string;
  id: string;
  label: string;
  lang: string;
  lat: string;
  lon: string;
  title: string;
  value: string;
  type: string;
};

type RType = {
  currentModule: SearchModuleType;
  onSelectHandler: {
    recents: Function;
    suggestions: Function;
    items: Function;
    location: Function;
    all: Function;
  };
  getQueryKey: (
    module: SearchModuleType,
    apiType: ApiType,
    ...values: string[]
  ) => string[];

  searchForText: (text: string, module?: SearchModuleType) => void;
  searchInCarouselItems: {
    label: string;
    src: string;
    alt: string;
    type: SearchModuleType;
  }[];
  removeRecentSearch: (
    key: string,
    { module, onSuccess }: { module?: SearchModuleType; onSuccess?: Function }
  ) => void;
  removeAllRecentSearches: (x: {
    moduleType?: 'GLOBAL' | 'JOBS' | 'PEOPLE' | 'POSTS' | 'PAGES' | 'LOCATIONS';
    onSuccess?: Function;
  }) => void;
  isSearchPage: boolean;
  isSearchAllPage: boolean;
  addToHistory: (item: any, mod: SearchModuleType) => void;
};
export type ApiType =
  | 'recents'
  | 'suggestions'
  | 'items'
  | 'location'
  | 'all'
  | 'locationSuggestions'
  | 'locationRecents';

export type TextArg = { params: { text: string } };

const useGlobalSearchUtilities = (): RType => {
  const location = useLocation();
  const { pathname } = location;
  const history = useHistory();
  const { t } = useTranslation();
  const isSearchAllPage = pathname.startsWith(routeNames.searchAll);
  const { handleHashtagClick } = useObjectClicks();
  const navigateSearchPage = useNavigateSearchPage();
  const currentModule: SearchModuleType =
    getModuleFromPathname(pathname) || defaultModule;
  const locationValueInFilter = useGetLocationValueInFilter();
  const isSearchPage = pathname.startsWith(routeNames.search);

  const queryClient = useQueryClient();
  const { setFilters, searchFilters, refetchWithCurrentEntityId } =
    useSearchFilters();

  const cacheKey = getQueryKey('all', 'recents', 'suggestions');
  const { addToHistory, removeRecentSearch, removeAllRecentSearches } =
    useSearchHistory(cacheKey);

  const search = (text: string, path: string) => {
    if (currentModule === 'all') {
      const searchParams = new URLSearchParams({
        [searchFilterQueryParams.query]: text,
      }).toString();

      return history.push(`${path}?${searchParams}`);
    }

    const isSameModule = path === pathname;
    const params = new URLSearchParams(
      JSON.parse(
        JSON.stringify({
          ...(isSameModule ? searchFilters : {}),
          [searchFilterQueryParams.searchGroupType]: searchGroupTypes.ALL,
          [searchFilterQueryParams.query]: encoder(text),
          [searchFilterQueryParams.currentEntityId]: undefined,
          [searchFilterQueryParams.page]: undefined,
        })
      )
    );
    history.push(`${path}?${params.toString()}`);
    queryClient.invalidateQueries(mutableStore.searchQueryKey);
  };

  const searchForText: RType['searchForText'] = (
    text: string,
    searchInmodule: SearchModuleType = currentModule || 'all'
  ) => {
    console.log({ text, searchInmodule });
    const isJobs = searchInmodule === 'jobs';
    text = text?.trim();
    const item = {
      title: text,
      value: text,
      imgUrl: '',
      label: text,
      type: 'TEXT',
      ...(isJobs && {
        cityCode: searchFilters?.cityCode,
        locationTitle: searchFilters?.locationTitle,
        stateCode: searchFilters?.stateCode,
        countryCode: searchFilters?.countryCode,
      }),
    };
    if (!isEmpty(text)) {
      addToHistory(item);
    }
    switch (searchInmodule) {
      case 'jobs':
        search(text, routeNames.searchJobs);
        break;
      case 'posts':
        search(text, routeNames.searchPosts);
        break;
      case 'pages':
        search(text, routeNames.searchPages);
        break;
      case 'people':
        search(text, routeNames.searchPeople);
        break;
      case 'projects':
        search(text, routeNames.searchRecruiterProjects);
        break;
      case 'recruiterJobs':
        search(text, routeNames.searchRecruiterJobs);
        break;
      case 'candidates':
        search(text, routeNames.searchCandidates);
        break;
      case 'hashtags':
        search(text, routeNames.searchPosts);
        break;
      case 'companies':
        search(text, routeNames.companies.search);
        break;
      default:
        search(text, routeNames.searchAll);
    }
  };
  const onSpecificSelectHandler = (item: SearchResultItem) => {
    addToHistory(item);
    switch (item?.type) {
      case 'PAGE':
      case 'PERSON': {
        history.push(routeNames.profile.replace(':username', item?.username));
        break;
      }
      case 'JOB': {
        navigateSearchPage({
          pathname: routeNames.searchJobs,
          query: item?.label,
          cityCode: item?.cityCode,
          locationTitle: item?.locationTitle,
          stateCode: item?.stateCode,
          countryCode: item?.countryCode,
        });
        break;
      }
      case 'CANDIDATE':
        // history.push(routeNames.profile.replace(':username', item?.username));
        navigateSearchPage({
          pathname: routeNames.searchCandidates,
          currentEntityId: item.id,
          query: item?.label,
        });
        break;
      case 'COMPANIES': {
        // history.push(routeNames.profile.replace(':username', item?.username));
        navigateSearchPage({
          pathname: routeNames.companies.search,
          currentEntityId: item.id,
          query: item?.label,
        });
        break;
      }
      case 'PROJECT': {
        navigateSearchPage({
          pathname: routeNames.searchRecruiterProjects,
          query: item?.label,
        });
        break;
      }
      case 'BUSINESS_JOB': {
        navigateSearchPage({
          pathname: routeNames.searchRecruiterJobs,
          query: item?.label,
          cityCode: item?.cityCode,
          locationTitle: item?.locationTitle,
          stateCode: item?.stateCode,
          countryCode: item?.countryCode,
        });
        break;
      }
      default: {
        alert('onSpecificSelectHandler default case, please report this issue');
      }
    }
    refetchWithCurrentEntityId();
  };
  const onNonSpecificSelectHandler = (item: SearchResultItem) => {
    switch (item?.type) {
      case 'JOB':
        {
          const isClickingAJobStringInsideJobsPage = currentModule === 'jobs';
          if (isClickingAJobStringInsideJobsPage) {
            searchForText(item?.label, 'jobs');
          } else {
            searchForText(item?.label, 'all');
          }
        }
        break;
      case 'HASHTAG': {
        handleHashtagClick(item?.label);
        break;
      }
      default: {
        searchForText(item?.label, 'all');
      }
    }
  };

  const onSelectHandler: RType['onSelectHandler'] = {
    recents: (item: SearchResultItem) => {
      const { isSpecific } = item;
      if (isSpecific) {
        onSpecificSelectHandler(item);
      } else {
        const isJobs = currentModule === 'jobs';
        searchForText(item?.label, isJobs ? 'jobs' : 'all');
      }
    },
    suggestions: (item: SearchResultItem) => {
      searchForText(item?.label);
    },
    items: (item: SearchResultItem) => {
      const { isSpecific } = item;
      if (isSpecific) {
        onSpecificSelectHandler(item);
      } else {
        onNonSpecificSelectHandler(item);
      }
    },
    location: (item: LocationItem) => {
      if (item.type === 'REMOTE') {
        return setFilters({
          ...searchFilters,
          [searchFilterQueryParams.workPlaceTypes]: 'REMOTE',
          [searchFilterQueryParams.countryCode]:
            locationValueInFilter.countryCodeDefault,
          [searchFilterQueryParams.locationTitle]:
            locationValueInFilter.placeTitleDefault,
          [searchFilterQueryParams.isLocationTitleFiltered]: undefined,
        });
      }
      let loc = {
        ...item,
        moduleType: 'LOCATIONS',
        entityType: 'LOCATION',
        name: item?.label,
      };
      if (!item?.dontSearchSet) {
        loc = findLocation(item);
      }
      addToHistory(loc);
      setFilters({
        ...searchFilters,
        [searchFilterQueryParams.currentEntityId]: undefined,
        [searchFilterQueryParams.countryCode]: loc?.value,
        [searchFilterQueryParams.locationTitle]: loc?.label,
        [searchFilterQueryParams.isLocationTitleFiltered]:
          loc?.label && !loc?.isDefault ? 'true' : undefined,
      });
    },
    all: (item: SearchResultItem) => {
      searchForText(item?.label, 'all');
    },
  };
  const searchInCarouselItems = [...modules, 'news', 'articles'].reduce(
    (prev, curr) => {
      if (
        curr === 'all' ||
        curr === 'hashtags' ||
        (curr === 'jobs' && !isRouterAccessibleByPortal(routeNames.searchJobs))
      ) {
        return prev;
      }
      const item = {
        label: t(curr),
        src: `${urls.storageBaseUrl}/assets/images/search-category/search-category-${curr}.png`,
        alt: t(curr),
        type: curr,
      };

      return [...prev, item];
    },
    []
  );
  // ?.sort((x, y) =>
  //   x.type === currentGeneralModule
  //     ? -1
  //     : y.type === currentGeneralModule
  //       ? 1
  //       : 0
  // );

  function getQueryKey(
    module: SearchModuleType,
    apiType: ApiType,
    ...values: string[]
  ): any[] {
    return ['SEARCH', module, currentModule, apiType, ...values];
  }

  const findLocation = (loc: any) => {
    if ((loc?.label && loc?.value) || isEmpty(loc)) {
      return loc;
    }
    const locations =
      (queryClient.getQueryData(['LOCATION', loc?.label]) as any[]) || [];

    return locations?.[0];
  };

  return {
    currentModule,
    onSelectHandler,
    getQueryKey,
    searchForText,
    searchInCarouselItems,
    removeRecentSearch,
    removeAllRecentSearches,
    isSearchPage,
    isSearchAllPage,
    addToHistory,
  };
};

export default useGlobalSearchUtilities;

const getModuleFromPathname = (
  pathname: string
): SearchModuleType | undefined => {
  for (const key in moduleBaseOnPathname) {
    if (pathname.includes(key)) {
      return moduleBaseOnPathname[key];
    }
  }

  return undefined;
};
