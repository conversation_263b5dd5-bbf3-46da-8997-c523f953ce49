import { useQueryClient } from '@tanstack/react-query';
import removeEmptyFromObject from '@shared/utils/toolkit/removeEmptyFromObject';
import depotApi from 'shared/utils/api/depot';
import useReactMutation from 'shared/utils/hooks/useReactMutation';
import { moveArray } from 'shared/utils/moveArray';

function makeUniqueLabel(item: any, isJobs: boolean) {
  return item?.label.concat(isJobs ? item?.locationId : '');
}

const useSearchHistory = (cacheKey: any[]) => {
  const queryClient = useQueryClient();
  const { mutate: addToHistoryMutation } = useReactMutation({
    apiFunc: depotApi.addSearchHistory,
  });
  const { mutate: removeRecentSearchMutation } = useReactMutation({
    apiFunc: depotApi.deleteSearchHistory,
  });
  const { mutate: removeAllRecentSearchesMutation } = useReactMutation({
    apiFunc: depotApi.deleteAllSearchHistory,
  });

  function updateCacheForAddToHistory(item: any, searchInmodule?: Module) {
    if (!item?.value) {
      return { isValid: false, order: null };
    }
    const historyData = (queryClient.getQueryData(cacheKey) as any) || {};
    const recents: any[] =
      historyData?.[
        searchInmodule === 'locations' ? 'recentLocations' : 'recents'
      ] || [];
    const isJobs = searchInmodule === 'jobs';
    const recentIds = recents
      ?.map((item) => item?.id)
      ?.slice(0, 2)
      ?.filter(Boolean);
    const recentLabels = recents
      ?.map((item) => makeUniqueLabel(item, isJobs))
      ?.slice(0, 2)
      ?.filter(Boolean);
    if (
      recentIds?.includes(item?.id) ||
      recentLabels?.includes(makeUniqueLabel(item, isJobs))
    ) {
      let order = recentIds?.findIndex((i) => i === item?.id);
      if (order === -1)
        order = recentLabels?.findIndex(
          (i) => i === makeUniqueLabel(item, isJobs)
        );
      if (order > -1) moveArray(recents, order, 0);
      queryClient.setQueryData(cacheKey, historyData);

      return { isValid: false, order };
    }
    recents.unshift(item);
    queryClient.setQueryData(cacheKey, historyData);

    return { isValid: true, order: null };
  }

  function addToHistory(item: any): void {
    // const { isValid, order } = updateCacheForAddToHistory(item, searchInmodule);
    // if (!isValid && !order) return;
    if (!item.moduleType || !item.entityType) {
      console.error('Invalid item to add to history', item);
      return;
    }
    const _item = {
      entityType: 'TEXT',
      moduleType: 'GLOBAL',
      ...item,
      locationName: item?.label,
      locationId: item?.value,
      label: item?.label || item?.title,
    };
    addToHistoryMutation(removeEmptyFromObject(_item), {
      onSettled: () => {
        queryClient.refetchQueries(cacheKey, { active: true });
      },
    });
  }

  function removeRecentSearch(
    key: string,
    { onSuccess, module }: { onSuccess?: Function; module?: Module }
  ): void {
    const historyData = queryClient.getQueryData(cacheKey) as any;
    const moduleKey = module === 'locations' ? 'recentLocations' : 'recents';
    const recents: any[] = historyData?.[moduleKey];
    const _recents = recents?.filter((item) => item?.key !== key);
    historyData[moduleKey] = _recents;
    queryClient.setQueryData(cacheKey, historyData);
    removeRecentSearchMutation(
      { key },
      {
        onSettled: () => {
          queryClient.refetchQueries(cacheKey, { active: true });
        },
        onSuccess: onSuccess as any,
      }
    );
  }

  function removeAllRecentSearches({
    moduleType = 'GLOBAL',
    onSuccess,
  }: {
    moduleType?: 'GLOBAL' | 'JOBS' | 'PEOPLE' | 'POSTS' | 'PAGES' | 'LOCATIONS';
    onSuccess?: Function;
  }): void {
    removeAllRecentSearchesMutation(
      {
        type: moduleType,
      },
      {
        onSettled: () => {
          queryClient.refetchQueries(cacheKey, { active: true });
        },
        onSuccess: onSuccess as any,
      }
    );
    const historyData = queryClient.getQueryData(cacheKey) as any;
    const moduleKey =
      moduleType === 'LOCATIONS' ? 'recentLocations' : 'recents';
    if (!historyData) return;
    historyData[moduleKey] = [];
    queryClient.setQueryData(cacheKey, historyData);
  }

  return {
    addToHistory,
    removeRecentSearch,
    removeAllRecentSearches,
  };
};

export default useSearchHistory;
