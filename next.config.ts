import type { NextConfig } from 'next';

const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});

const isLocal = process.env.NEXT_PUBLIC_DOMAIN === 'localhost';

const withWorkbox = isLocal
  ? (config: NextConfig) => config
  : require('next-with-workbox');

const nextConfig: NextConfig = {
  experimental: {
    reactCompiler: false,
    optimizePackageImports: ['lodash', 'date-fns'],
  },
  serverExternalPackages: ['sharp'],
  assetPrefix: !isLocal ? process.env.ASSET_PREFIX : undefined,
  logging: {
    fetches: {
      fullUrl: true,
    },
  },
  /* ------------------------ Performance optimizations ----------------------- */
  compress: true,
  // poweredByHeader: false,
  generateEtags: true,
  /* ------------------------ Performance optimizations ----------------------- */
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    formats: ['image/webp'],
    minimumCacheTTL: 60 * 60 * 24, // 1day
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'storage.googleapis.com',
        pathname: '/lobox_public_images/**',
      },
      {
        protocol: 'https',
        hostname: 'cdn.lobox.com',
        pathname: '/assets/images/**',
      },
      { protocol: 'https', hostname: 'cdn.lobox.com', pathname: '/image/**' },
      {
        protocol: 'https',
        hostname: 'storage.googleapis.com',
        pathname: '/**',
      },
    ],
  },
  reactStrictMode: false,
  webpack: (config) => {
    config.resolve.alias.canvas = false;

    return config;
  },
  async rewrites() {
    const pdfViewerBaseUrl =
      process.env.NEXT_PUBLIC_PROD_ENV === 'production'
        ? 'https://lobox.com'
        : 'https://dev.lobox.com';

    return [
      {
        source: '/pdf-viewer/:path*',
        destination: `${pdfViewerBaseUrl}/pdf-viewer/:path*`,
      },
    ];
  },
};

module.exports = withBundleAnalyzer(
  withWorkbox({
    workbox: !isLocal
      ? {
          dest: 'public',
          swDest: 'sw.js',
          swSrc: 'service-worker.ts',
          force: true,
        }
      : undefined,
    ...nextConfig,
  })
);
